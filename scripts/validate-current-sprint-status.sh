#!/bin/bash

# 🔍 VALIDATION STATUT SPRINT ACTUEL
# Vérifie l'état d'avancement des sprints et actions manquantes

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="$PROJECT_ROOT/sprint-validation-reports"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Compteurs
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Fonctions utilitaires
log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }

header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║$(printf "%*s" $(((78-${#1})/2)) "")${1}$(printf "%*s" $(((78-${#1})/2)) "")║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

check_item() {
    local description="$1"
    local command="$2"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command" &>/dev/null; then
        success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        error "$description"
        return 1
    fi
}

# Validation Sprint 16
validate_sprint16() {
    header "📊 VALIDATION SPRINT 16 - ANALYTICS ET IA"
    
    log "Vérification Sprint 16 (Analytics et IA prédictive)..."
    
    check_item "Sprint 16 - Jour 1 complété" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint16/SPRINT16_DAY1_PROGRESS.md' ]]"
    
    check_item "Sprint 16 - Jour 2 complété" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint16/SPRINT16_DAY2_PROGRESS.md' ]]"
    
    check_item "Sprint 16 - Jour 3 complété" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint16/SPRINT16_DAY3_PROGRESS.md' ]]"
    
    check_item "Sprint 16 - Finalisation complète" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint16/SPRINT16_DAY4_FINAL.md' ]]"
    
    check_item "Sprint 16 - Validation report" \
        "[[ -f '$PROJECT_ROOT/sprint-16-validation/validation-report-'*'.md' ]]"
    
    check_item "Sprint 16 - Scripts d'implémentation" \
        "[[ -f '$PROJECT_ROOT/scripts/validate-sprint16-progress.sh' ]]"
}

# Validation Sprint 17
validate_sprint17() {
    header "📱 VALIDATION SPRINT 17 - MOBILE-FIRST REVOLUTION"
    
    log "Vérification Sprint 17 (Mobile-First Revolution)..."
    
    check_item "Sprint 17 - Plan d'implémentation" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint17/SPRINT17_IMPLEMENTATION_PLAN.md' ]]"
    
    check_item "Sprint 17 - Script de démarrage" \
        "[[ -f '$PROJECT_ROOT/scripts/start-sprint17-mobile-revolution.sh' && -x '$PROJECT_ROOT/scripts/start-sprint17-mobile-revolution.sh' ]]"
    
    check_item "Sprint 17 - Script Jour 2" \
        "[[ -f '$PROJECT_ROOT/scripts/continue-sprint17-day2.sh' && -x '$PROJECT_ROOT/scripts/continue-sprint17-day2.sh' ]]"
    
    check_item "Sprint 17 - Structure dossiers" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint17' ]]"
    
    # Vérifier si Sprint 17 a été démarré
    if [[ -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then
        success "Sprint 17 - Service Worker configuré (Jour 1 démarré)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        warning "Sprint 17 - Pas encore démarré (Service Worker manquant)"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # Vérifier configuration mobile
    if [[ -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts" ]]; then
        success "Sprint 17 - Configuration mobile Vite (Jour 2 démarré)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        warning "Sprint 17 - Configuration mobile pas encore créée"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

# Validation Sprints 18-20
validate_future_sprints() {
    header "🌍 VALIDATION SPRINTS FUTURS (18-20)"
    
    log "Vérification préparation Sprints 18-20..."
    
    check_item "Sprints 18-20 - Plan d'action détaillé" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/PLAN_ACTION_SPRINTS_17-20.md' ]]"
    
    check_item "Sprint 18 - Ecosystem Expansion planifié" \
        "grep -q 'SPRINT 18 - ECOSYSTEM EXPANSION' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/PLAN_ACTION_SPRINTS_17-20.md'"
    
    check_item "Sprint 19 - AI-Driven Automation planifié" \
        "grep -q 'SPRINT 19 - AI-DRIVEN AUTOMATION' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/PLAN_ACTION_SPRINTS_17-20.md'"
    
    check_item "Sprint 20 - Global Scale Deployment planifié" \
        "grep -q 'SPRINT 20 - GLOBAL SCALE DEPLOYMENT' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/PLAN_ACTION_SPRINTS_17-20.md'"
}

# Validation infrastructure Hanuman
validate_hanuman_infrastructure() {
    header "🤖 VALIDATION INFRASTRUCTURE HANUMAN"
    
    log "Vérification infrastructure Hanuman..."
    
    check_item "Hanuman - Système unifié" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified' ]]"
    
    check_item "Hanuman - Cortex Central" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/cortex-central' ]]"
    
    check_item "Hanuman - Agents spécialisés" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/agent-frontend' && -d '$PROJECT_ROOT/hanuman-unified/agent-backend' ]]"
    
    check_item "Hanuman - Sandbox roadmap" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap' ]]"
    
    check_item "Hanuman - Scripts d'intégration" \
        "[[ -f '$PROJECT_ROOT/scripts/start-hanuman.sh' ]]"
    
    check_item "Hanuman - Monitoring configuré" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/ROADMAP_IMPLEMENTATION_STATUS.md' ]]"
}

# Validation projet RB2
validate_rb2_project() {
    header "🏠 VALIDATION PROJET RETREAT AND BE"
    
    log "Vérification projet Retreat And Be..."
    
    check_item "RB2 - Frontend principal" \
        "[[ -d '$PROJECT_ROOT/Front-Audrey-V1-Main-main' ]]"
    
    check_item "RB2 - Backend NestJS" \
        "[[ -d '$PROJECT_ROOT/Projet-RB2/Backend-NestJS' ]]"
    
    check_item "RB2 - Roadmaps complètes" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/ROADMAP-FINAL-2024.md' ]]"
    
    check_item "RB2 - Documentation sprints" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/sprint_summaries/implementation_progress.md' ]]"
    
    check_item "RB2 - Système de modération" \
        "[[ -d '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src' ]]"
}

# Validation actions manquantes
identify_missing_actions() {
    header "🎯 IDENTIFICATION ACTIONS MANQUANTES"
    
    log "Analyse des actions manquantes..."
    
    local missing_actions=()
    
    # Sprint 17 - Mobile Revolution
    if [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then
        missing_actions+=("🚀 Démarrer Sprint 17 - Mobile-First Revolution")
    elif [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts" ]]; then
        missing_actions+=("⚡ Continuer Sprint 17 Jour 2 - Performance Mobile")
    elif [[ ! -d "$PROJECT_ROOT/Front-Audrey-V1-Main-main/src/mobile/components" ]]; then
        missing_actions+=("🎨 Continuer Sprint 17 Jour 3 - Design System Mobile")
    fi
    
    # Sprints futurs
    if [[ ! -d "$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint18" ]]; then
        missing_actions+=("🌍 Préparer Sprint 18 - Ecosystem Expansion")
    fi
    
    if [[ ! -d "$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint19" ]]; then
        missing_actions+=("🤖 Préparer Sprint 19 - AI-Driven Automation")
    fi
    
    if [[ ! -d "$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint20" ]]; then
        missing_actions+=("🌍 Préparer Sprint 20 - Global Scale Deployment")
    fi
    
    # Afficher les actions manquantes
    if [[ ${#missing_actions[@]} -gt 0 ]]; then
        echo ""
        echo -e "${YELLOW}📋 ACTIONS MANQUANTES IDENTIFIÉES:${NC}"
        for action in "${missing_actions[@]}"; do
            echo -e "${YELLOW}  • $action${NC}"
        done
    else
        echo ""
        success "🎉 Aucune action manquante identifiée !"
    fi
    
    return ${#missing_actions[@]}
}

# Recommandations d'actions
provide_recommendations() {
    header "💡 RECOMMANDATIONS D'ACTIONS"
    
    log "Génération des recommandations..."
    
    local score_percentage=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    
    echo ""
    echo -e "${CYAN}📊 SCORE GLOBAL: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)${NC}"
    echo ""
    
    if [[ $score_percentage -ge 90 ]]; then
        echo -e "${GREEN}🎉 EXCELLENT ÉTAT DU PROJET !${NC}"
        echo -e "${GREEN}• Tous les systèmes sont opérationnels${NC}"
        echo -e "${GREEN}• Prêt pour les prochaines étapes${NC}"
        echo ""
        echo -e "${BLUE}🚀 PROCHAINES ACTIONS RECOMMANDÉES:${NC}"
        
        if [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then
            echo -e "${BLUE}1. Démarrer Sprint 17: ./scripts/start-sprint17-mobile-revolution.sh${NC}"
        elif [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts" ]]; then
            echo -e "${BLUE}1. Continuer Sprint 17 Jour 2: ./scripts/continue-sprint17-day2.sh${NC}"
        else
            echo -e "${BLUE}1. Continuer Sprint 17 selon planning${NC}"
        fi
        
    elif [[ $score_percentage -ge 70 ]]; then
        echo -e "${YELLOW}⚠️ ÉTAT CORRECT - QUELQUES AJUSTEMENTS NÉCESSAIRES${NC}"
        echo -e "${YELLOW}• La plupart des systèmes sont en place${NC}"
        echo -e "${YELLOW}• Corrections mineures recommandées${NC}"
        
    else
        echo -e "${RED}❌ ÉTAT CRITIQUE - ACTIONS CORRECTIVES REQUISES${NC}"
        echo -e "${RED}• Plusieurs systèmes manquants ou incomplets${NC}"
        echo -e "${RED}• Révision majeure nécessaire${NC}"
    fi
}

# Génération du rapport de validation
generate_validation_report() {
    header "📊 GÉNÉRATION RAPPORT DE VALIDATION"
    
    mkdir -p "$REPORTS_DIR"
    local report_file="$REPORTS_DIR/sprint-status-validation-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 🔍 RAPPORT DE VALIDATION STATUT SPRINTS

**📅 Date:** $(date)  
**🎯 Validation:** État d'avancement global  
**📊 Score:** $PASSED_CHECKS/$TOTAL_CHECKS ($(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))%)

## 📊 RÉSUMÉ EXÉCUTIF

### Score Global: $(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))%
- **Vérifications réussies:** $PASSED_CHECKS
- **Vérifications totales:** $TOTAL_CHECKS
- **Statut:** $(if [ $(( (PASSED_CHECKS * 100) / TOTAL_CHECKS )) -ge 90 ]; then echo "✅ EXCELLENT"; elif [ $(( (PASSED_CHECKS * 100) / TOTAL_CHECKS )) -ge 70 ]; then echo "⚠️ CORRECT"; else echo "❌ CRITIQUE"; fi)

## 🔍 DÉTAILS PAR SPRINT

### ✅ Sprint 16 - Analytics et IA Prédictive
- Infrastructure analytics complète
- IA prédictive opérationnelle
- Performance optimisée
- Documentation complète

### 📱 Sprint 17 - Mobile-First Revolution
- Plan d'implémentation créé
- Scripts de démarrage prêts
- Structure organisée
- $(if [[ -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then echo "PWA Foundation démarrée"; else echo "En attente de démarrage"; fi)

### 🌍 Sprints 18-20 - Expansion Globale
- Roadmap détaillée planifiée
- Objectifs définis
- Architecture préparée
- En attente d'exécution

## 💡 RECOMMANDATIONS

### Actions Immédiates
$(if [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then
    echo "1. **Démarrer Sprint 17**: \`./scripts/start-sprint17-mobile-revolution.sh\`"
elif [[ ! -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts" ]]; then
    echo "1. **Continuer Sprint 17 Jour 2**: \`./scripts/continue-sprint17-day2.sh\`"
else
    echo "1. **Continuer Sprint 17**: Suivre le planning jour par jour"
fi)

2. **Monitoring continu**: Surveiller les métriques de performance
3. **Tests réguliers**: Valider chaque étape avant progression

### Actions à Moyen Terme
1. **Préparer Sprint 18**: Ecosystem Expansion
2. **Planifier Sprint 19**: AI-Driven Automation  
3. **Organiser Sprint 20**: Global Scale Deployment

## 🎯 PROCHAINES ÉTAPES

1. **Immédiat**: Continuer Sprint 17 selon planning
2. **Cette semaine**: Finaliser Mobile-First Revolution
3. **Semaine prochaine**: Démarrer Ecosystem Expansion

---

**Validation générée le $(date) par validate-current-sprint-status.sh**
EOF

    success "Rapport de validation généré: $report_file"
}

# Fonction principale
main() {
    header "🔍 VALIDATION STATUT SPRINT ACTUEL"
    
    log "Démarrage validation complète..."
    
    # Exécuter toutes les validations
    validate_sprint16
    validate_sprint17
    validate_future_sprints
    validate_hanuman_infrastructure
    validate_rb2_project
    
    # Identifier les actions manquantes
    identify_missing_actions
    
    # Fournir des recommandations
    provide_recommendations
    
    # Générer le rapport
    generate_validation_report
    
    echo ""
    success "🎉 VALIDATION TERMINÉE"
    log "📊 Score final: $PASSED_CHECKS/$TOTAL_CHECKS"
    log "📄 Rapport détaillé: $REPORTS_DIR"
}

# Exécution du script
main "$@"
