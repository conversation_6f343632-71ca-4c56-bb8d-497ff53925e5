#!/bin/bash

# 📱 SCRIPT DE DÉMARRAGE SPRINT 17 - MOBILE-FIRST REVOLUTION
# Date: 7 Juin 2025
# Objectif: Transformer Retreat And Be en leader mobile

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SPRINT_DIR="$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint17"
FRONTEND_DIR="$PROJECT_ROOT/Front-Audrey-V1-Main-main"
BACKEND_DIR="$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
REPORTS_DIR="$PROJECT_ROOT/sprint-17-reports"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions utilitaires
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║$(printf "%78s" | tr ' ' ' ')║${NC}"
    echo -e "${PURPLE}║$(printf "%*s" $(((78-${#1})/2)) "")${1}$(printf "%*s" $(((78-${#1})/2)) "")║${NC}"
    echo -e "${PURPLE}║$(printf "%78s" | tr ' ' ' ')║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Vérification des prérequis
check_prerequisites() {
    header "🔍 VÉRIFICATION DES PRÉREQUIS"
    
    log "Vérification de l'environnement..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        error "npm n'est pas installé"
    fi
    
    # Vérifier que Sprint 16 est terminé
    if [[ ! -f "$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint16/SPRINT16_DAY4_FINAL.md" ]]; then
        error "Sprint 16 doit être terminé avant de commencer Sprint 17"
    fi
    
    success "Prérequis validés"
}

# Création de la structure Sprint 17
create_sprint_structure() {
    header "🏗️ CRÉATION STRUCTURE SPRINT 17"
    
    log "Création des dossiers Sprint 17..."
    
    # Créer les dossiers
    mkdir -p "$SPRINT_DIR"/{day1,day2,day3,day4,day5,day6-7}
    mkdir -p "$REPORTS_DIR"
    mkdir -p "$FRONTEND_DIR/src/mobile"/{pwa,components,services,utils}
    mkdir -p "$FRONTEND_DIR/public/pwa-assets"
    
    success "Structure Sprint 17 créée"
}

# Configuration PWA Foundation (Jour 1)
setup_pwa_foundation() {
    header "📱 JOUR 1 - PWA FOUNDATION"
    
    log "Configuration Service Worker..."
    
    # Service Worker
    cat > "$FRONTEND_DIR/public/sw.js" << 'EOF'
// Service Worker pour PWA Retreat And Be
// Version: 1.0.0 - Sprint 17

const CACHE_NAME = 'retreat-and-be-v1';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';

// Ressources à mettre en cache
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/static/css/main.css',
  '/static/js/main.js',
  '/pwa-assets/icon-192x192.png',
  '/pwa-assets/icon-512x512.png'
];

// Installation du Service Worker
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activation du Service Worker
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Stratégies de cache
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Cache First pour les assets statiques
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then(response => response || fetch(request))
    );
    return;
  }

  // Network First pour les API calls
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then(response => {
          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE)
            .then(cache => cache.put(request, responseClone));
          return response;
        })
        .catch(() => caches.match(request))
    );
    return;
  }

  // Stale While Revalidate pour le reste
  event.respondWith(
    caches.match(request)
      .then(response => {
        const fetchPromise = fetch(request)
          .then(networkResponse => {
            caches.open(DYNAMIC_CACHE)
              .then(cache => cache.put(request, networkResponse.clone()));
            return networkResponse;
          });
        return response || fetchPromise;
      })
  );
});

// Background Sync
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    console.log('Service Worker: Background sync triggered');
    event.waitUntil(doBackgroundSync());
  }
});

// Push Notifications
self.addEventListener('push', event => {
  const options = {
    body: event.data ? event.data.text() : 'Nouvelle notification',
    icon: '/pwa-assets/icon-192x192.png',
    badge: '/pwa-assets/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Explorer',
        icon: '/pwa-assets/checkmark.png'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/pwa-assets/xmark.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('Retreat And Be', options)
  );
});

// Fonctions utilitaires
async function doBackgroundSync() {
  // Synchronisation en arrière-plan
  try {
    const response = await fetch('/api/sync');
    console.log('Background sync successful');
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}
EOF

    # Manifest PWA
    cat > "$FRONTEND_DIR/public/manifest.json" << 'EOF'
{
  "name": "Retreat And Be",
  "short_name": "RetreatBe",
  "description": "Plateforme de retraites et bien-être",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#4f46e5",
  "orientation": "portrait-primary",
  "scope": "/",
  "lang": "fr",
  "categories": ["lifestyle", "health", "travel"],
  "icons": [
    {
      "src": "/pwa-assets/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any maskable"
    },
    {
      "src": "/pwa-assets/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png"
    },
    {
      "src": "/pwa-assets/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "any maskable"
    }
  ],
  "shortcuts": [
    {
      "name": "Rechercher une retraite",
      "short_name": "Rechercher",
      "description": "Trouver la retraite parfaite",
      "url": "/search",
      "icons": [
        {
          "src": "/pwa-assets/search-icon.png",
          "sizes": "96x96"
        }
      ]
    },
    {
      "name": "Mes réservations",
      "short_name": "Réservations",
      "description": "Voir mes réservations",
      "url": "/bookings",
      "icons": [
        {
          "src": "/pwa-assets/booking-icon.png",
          "sizes": "96x96"
        }
      ]
    }
  ],
  "screenshots": [
    {
      "src": "/pwa-assets/screenshot-mobile.png",
      "sizes": "540x720",
      "type": "image/png",
      "form_factor": "narrow"
    },
    {
      "src": "/pwa-assets/screenshot-desktop.png",
      "sizes": "1280x720",
      "type": "image/png",
      "form_factor": "wide"
    }
  ]
}
EOF

    success "PWA Foundation configurée"
}

# Installation des dépendances mobile
install_mobile_dependencies() {
    header "📦 INSTALLATION DÉPENDANCES MOBILE"
    
    log "Installation des packages PWA..."
    
    cd "$FRONTEND_DIR"
    
    # Packages PWA essentiels
    npm install --save-dev workbox-cli workbox-webpack-plugin
    npm install workbox-window workbox-strategies workbox-routing
    npm install @types/serviceworker
    
    # Packages performance mobile
    npm install react-intersection-observer react-window react-virtualized-auto-sizer
    npm install @loadable/component
    
    # Packages géolocalisation
    npm install leaflet react-leaflet @types/leaflet
    
    success "Dépendances mobile installées"
}

# Génération du rapport de démarrage
generate_startup_report() {
    header "📊 GÉNÉRATION RAPPORT DE DÉMARRAGE"
    
    local report_file="$REPORTS_DIR/sprint17-startup-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 📱 RAPPORT DE DÉMARRAGE SPRINT 17

**Date**: $(date)  
**Sprint**: 17 - Mobile-First Revolution  
**Statut**: 🚀 **DÉMARRÉ AVEC SUCCÈS**

## 📊 RÉSUMÉ EXÉCUTIF

### Objectifs Sprint 17
- 📱 PWA révolutionnaire avec performance <100ms
- 🔄 Service Worker intelligent avec cache avancé
- 📲 Push notifications et géolocalisation
- 🎨 Design System mobile-first
- ⚡ Optimisations performance agressives

### Structure Créée
- ✅ Dossiers Sprint 17 organisés
- ✅ Service Worker configuré
- ✅ Manifest PWA complet
- ✅ Dépendances mobile installées
- ✅ Architecture PWA établie

## 🏗️ ARCHITECTURE PWA

### Service Worker
- Cache intelligent multi-stratégies
- Background sync configuré
- Push notifications prêtes
- Offline fallbacks

### Manifest PWA
- Icons adaptatifs (72px à 512px)
- Shortcuts d'application
- Screenshots pour stores
- Métadonnées complètes

## 📋 PROCHAINES ÉTAPES

### Jour 1 (Aujourd'hui)
- [x] PWA Foundation
- [ ] Tests Service Worker
- [ ] Validation cache strategies
- [ ] Optimisation initiale

### Jour 2 (Demain)
- [ ] Performance optimization
- [ ] Bundle splitting mobile
- [ ] Image optimization
- [ ] Critical CSS

## 🎯 MÉTRIQUES CIBLES

- **Performance**: <100ms navigation mobile
- **PWA Score**: 100/100 Lighthouse
- **Cache Hit**: >90%
- **Bundle Size**: <500KB initial

---

**🚀 Sprint 17 démarré avec succès !**
*Équipe prête pour la révolution mobile*
EOF

    success "Rapport de démarrage généré: $report_file"
}

# Fonction principale
main() {
    header "🚀 DÉMARRAGE SPRINT 17 - MOBILE-FIRST REVOLUTION"
    
    log "Initialisation Sprint 17..."
    
    # Exécuter toutes les étapes
    check_prerequisites
    create_sprint_structure
    setup_pwa_foundation
    install_mobile_dependencies
    generate_startup_report
    
    # Message de succès final
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                                                                              ║${NC}"
    echo -e "${GREEN}║                    🎉 SPRINT 17 DÉMARRÉ AVEC SUCCÈS ! 🎉                    ║${NC}"
    echo -e "${GREEN}║                                                                              ║${NC}"
    echo -e "${GREEN}║  📱 PWA Foundation configurée                                                ║${NC}"
    echo -e "${GREEN}║  🔄 Service Worker intelligent activé                                       ║${NC}"
    echo -e "${GREEN}║  📦 Dépendances mobile installées                                           ║${NC}"
    echo -e "${GREEN}║  🏗️ Architecture PWA établie                                                ║${NC}"
    echo -e "${GREEN}║                                                                              ║${NC}"
    echo -e "${GREEN}║  🎯 Objectif: Performance mobile <100ms                                     ║${NC}"
    echo -e "${GREEN}║  🚀 Direction: Révolution mobile en cours !                                 ║${NC}"
    echo -e "${GREEN}║                                                                              ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    log "📊 Rapport détaillé disponible dans: $REPORTS_DIR"
    log "📱 Prochaine étape: Continuer avec l'optimisation performance (Jour 2)"
    log "🔧 Commande suivante: ./scripts/continue-sprint17-day2.sh"
}

# Exécution du script
main "$@"
