#!/bin/bash

# 🔍 VALIDATION FINALE - PRÊT POUR SPRINTS 18-20
# Vérification complète avant exécution expansion globale

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VALIDATION_REPORT="$PROJECT_ROOT/SPRINTS_18-20_READINESS_REPORT.md"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Compteurs
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Fonctions utilitaires
log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }

header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║$(printf "%*s" $(((78-${#1})/2)) "")${1}$(printf "%*s" $(((78-${#1})/2)) "")║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

check_item() {
    local description="$1"
    local command="$2"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command" &>/dev/null; then
        success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        error "$description"
        return 1
    fi
}

# Validation Sprint 17 (prérequis)
validate_sprint17_prerequisite() {
    header "📱 VALIDATION PRÉREQUIS SPRINT 17"
    
    log "Vérification que Sprint 17 est opérationnel..."
    
    check_item "Sprint 17 - PWA Foundation créée" \
        "[[ -f '$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js' ]]"
    
    check_item "Sprint 17 - Manifest PWA configuré" \
        "[[ -f '$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/manifest.json' ]]"
    
    check_item "Sprint 17 - Configuration mobile Vite" \
        "[[ -f '$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts' ]]"
    
    check_item "Sprint 17 - Structure mobile créée" \
        "[[ -d '$PROJECT_ROOT/Front-Audrey-V1-Main-main/src/mobile' ]]"
}

# Validation Sprint 18 - Ecosystem Expansion
validate_sprint18_readiness() {
    header "🌐 VALIDATION SPRINT 18 - ECOSYSTEM EXPANSION"
    
    log "Vérification préparation Sprint 18..."
    
    check_item "Sprint 18 - Plan d'implémentation" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint18/SPRINT18_IMPLEMENTATION_PLAN.md' ]]"
    
    check_item "Sprint 18 - Script de démarrage" \
        "[[ -f '$PROJECT_ROOT/scripts/start-sprint18-ecosystem-expansion.sh' && -x '$PROJECT_ROOT/scripts/start-sprint18-ecosystem-expansion.sh' ]]"
    
    check_item "Sprint 18 - Structure dossiers" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint18' ]]"
    
    check_item "Sprint 18 - Backend NestJS prêt" \
        "[[ -d '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src' ]]"
}

# Validation Sprint 19 - AI-Driven Automation
validate_sprint19_readiness() {
    header "🤖 VALIDATION SPRINT 19 - AI-DRIVEN AUTOMATION"
    
    log "Vérification préparation Sprint 19..."
    
    check_item "Sprint 19 - Plan d'implémentation" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint19/SPRINT19_IMPLEMENTATION_PLAN.md' ]]"
    
    check_item "Sprint 19 - Structure dossiers" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint19' ]]"
    
    check_item "Sprint 19 - Architecture IA planifiée" \
        "grep -q 'GPT-4' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint19/SPRINT19_IMPLEMENTATION_PLAN.md'"
    
    check_item "Sprint 19 - Automatisation définie" \
        "grep -q 'automation' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint19/SPRINT19_IMPLEMENTATION_PLAN.md'"
}

# Validation Sprint 20 - Global Scale Deployment
validate_sprint20_readiness() {
    header "🌍 VALIDATION SPRINT 20 - GLOBAL SCALE DEPLOYMENT"
    
    log "Vérification préparation Sprint 20..."
    
    check_item "Sprint 20 - Plan d'implémentation" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint20/SPRINT20_IMPLEMENTATION_PLAN.md' ]]"
    
    check_item "Sprint 20 - Structure dossiers" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint20' ]]"
    
    check_item "Sprint 20 - Infrastructure globale planifiée" \
        "grep -q 'multi-region' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint20/SPRINT20_IMPLEMENTATION_PLAN.md'"
    
    check_item "Sprint 20 - Conformité réglementaire définie" \
        "grep -q 'GDPR' '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/sprint20/SPRINT20_IMPLEMENTATION_PLAN.md'"
}

# Validation scripts et outils
validate_scripts_and_tools() {
    header "🛠️ VALIDATION SCRIPTS ET OUTILS"
    
    log "Vérification scripts d'automatisation..."
    
    check_item "Gestionnaire Sprints 18-20" \
        "[[ -f '$PROJECT_ROOT/scripts/manage-sprints-18-20.sh' && -x '$PROJECT_ROOT/scripts/manage-sprints-18-20.sh' ]]"
    
    check_item "Script validation Sprint 17" \
        "[[ -f '$PROJECT_ROOT/scripts/validate-current-sprint-status.sh' && -x '$PROJECT_ROOT/scripts/validate-current-sprint-status.sh' ]]"
    
    check_item "Script démarrage rapide Sprint 17" \
        "[[ -f '$PROJECT_ROOT/scripts/quick-start-sprint17.sh' && -x '$PROJECT_ROOT/scripts/quick-start-sprint17.sh' ]]"
    
    check_item "Documentation complète implémentée" \
        "[[ -f '$PROJECT_ROOT/SPRINTS_18-20_IMPLEMENTATION_COMPLETE.md' ]]"
}

# Validation infrastructure Hanuman
validate_hanuman_infrastructure() {
    header "🤖 VALIDATION INFRASTRUCTURE HANUMAN"
    
    log "Vérification infrastructure Hanuman..."
    
    check_item "Hanuman - Système unifié" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified' ]]"
    
    check_item "Hanuman - Sandbox roadmap" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap' ]]"
    
    check_item "Hanuman - Plan d'action Sprints 17-20" \
        "[[ -f '$PROJECT_ROOT/hanuman-unified/sandbox/roadmap/PLAN_ACTION_SPRINTS_17-20.md' ]]"
    
    check_item "Hanuman - Agents spécialisés" \
        "[[ -d '$PROJECT_ROOT/hanuman-unified/agent-frontend' || -d '$PROJECT_ROOT/hanuman-unified/agent-backend' ]]"
}

# Validation environnement technique
validate_technical_environment() {
    header "💻 VALIDATION ENVIRONNEMENT TECHNIQUE"
    
    log "Vérification environnement de développement..."
    
    check_item "Node.js installé" \
        "command -v node"
    
    check_item "npm installé" \
        "command -v npm"
    
    check_item "Git installé" \
        "command -v git"
    
    # Docker optionnel mais recommandé
    if command -v docker &> /dev/null; then
        success "Docker installé (recommandé)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        warning "Docker non installé (optionnel mais recommandé)"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

# Génération rapport de validation
generate_readiness_report() {
    header "📊 GÉNÉRATION RAPPORT DE VALIDATION"
    
    local score_percentage=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    
    cat > "$VALIDATION_REPORT" << EOF
# 🔍 RAPPORT DE VALIDATION - PRÊT POUR SPRINTS 18-20

**Date**: $(date)  
**Score Global**: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)  
**Statut**: $(if [ $score_percentage -ge 90 ]; then echo "✅ PRÊT POUR EXÉCUTION"; elif [ $score_percentage -ge 70 ]; then echo "⚠️ PRÊT AVEC AJUSTEMENTS"; else echo "❌ PRÉPARATION REQUISE"; fi)

---

## 📊 RÉSUMÉ EXÉCUTIF

### Score de Préparation: $score_percentage%
$(if [ $score_percentage -ge 90 ]; then
    echo "🎉 **EXCELLENT** - Tous les systèmes sont prêts pour l'expansion globale"
elif [ $score_percentage -ge 70 ]; then
    echo "⚠️ **CORRECT** - Quelques ajustements mineurs recommandés"
else
    echo "❌ **CRITIQUE** - Préparation supplémentaire requise"
fi)

### Prérequis Validés
- Sprint 17 Mobile-First opérationnel
- Plans d'implémentation Sprints 18-20 complets
- Scripts d'automatisation créés
- Infrastructure Hanuman prête

## 🎯 PROCHAINES ACTIONS RECOMMANDÉES

### Action Immédiate
$(if [ $score_percentage -ge 90 ]; then
    echo "\`\`\`bash"
    echo "# Démarrer expansion globale immédiatement"
    echo "./scripts/manage-sprints-18-20.sh"
    echo "\`\`\`"
elif [ $score_percentage -ge 70 ]; then
    echo "1. Résoudre les points d'attention identifiés"
    echo "2. Relancer la validation"
    echo "3. Démarrer l'expansion globale"
else
    echo "1. Compléter les éléments manquants critiques"
    echo "2. Finaliser Sprint 17 si nécessaire"
    echo "3. Relancer la validation complète"
fi)

### Planning Recommandé
- **Aujourd'hui**: $(if [ $score_percentage -ge 90 ]; then echo "Démarrer Sprint 18"; else echo "Finaliser préparation"; fi)
- **14 Juin**: Sprint 18 - Ecosystem Expansion
- **21 Juin**: Sprint 19 - AI-Driven Automation
- **28 Juin**: Sprint 20 - Global Scale Deployment
- **4 Juillet**: 🎉 LANCEMENT GLOBAL !

## 🌍 OBJECTIFS EXPANSION GLOBALE

### Sprint 18 - Ecosystem Expansion
- 🌐 API Gateway Kong enterprise
- 🤝 Intégrations partenaires majeures
- 🛒 Marketplace d'extensions
- 🏷️ Solutions white-label

### Sprint 19 - AI-Driven Automation
- 🤖 GPT-4 integration complète
- 🔄 Automatisation journey
- 🎯 Personnalisation hyper-ciblée
- 📊 Optimisation automatique

### Sprint 20 - Global Scale Deployment
- 🌍 Infrastructure multi-régions
- ⚖️ Conformité réglementaire globale
- 💰 Paiements 50+ devises
- 🗣️ Support 15+ langues

---

**🚀 RETREAT AND BE - PRÊT POUR DOMINATION MONDIALE !**

*Validation effectuée le $(date)*
*Score: $score_percentage% - $(if [ $score_percentage -ge 90 ]; then echo "EXCELLENT"; elif [ $score_percentage -ge 70 ]; then echo "CORRECT"; else echo "À AMÉLIORER"; fi)*
EOF

    success "Rapport de validation généré: $VALIDATION_REPORT"
}

# Affichage résultats finaux
show_final_results() {
    local score_percentage=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    
    echo ""
    if [ $score_percentage -ge 90 ]; then
        echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${GREEN}║                                                                              ║${NC}"
        echo -e "${GREEN}║                    🎉 VALIDATION RÉUSSIE - PRÊT À EXÉCUTER ! 🎉             ║${NC}"
        echo -e "${GREEN}║                                                                              ║${NC}"
        echo -e "${GREEN}║  ✅ Score: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)                                                    ║${NC}"
        echo -e "${GREEN}║  🚀 Sprints 18-20 prêts pour exécution immédiate                           ║${NC}"
        echo -e "${GREEN}║  🌍 Expansion globale peut commencer maintenant                             ║${NC}"
        echo -e "${GREEN}║                                                                              ║${NC}"
        echo -e "${GREEN}║  🎯 Commande: ./scripts/manage-sprints-18-20.sh                            ║${NC}"
        echo -e "${GREEN}║                                                                              ║${NC}"
        echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    elif [ $score_percentage -ge 70 ]; then
        echo -e "${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${YELLOW}║                                                                              ║${NC}"
        echo -e "${YELLOW}║                    ⚠️ VALIDATION CORRECTE - AJUSTEMENTS MINEURS ⚠️           ║${NC}"
        echo -e "${YELLOW}║                                                                              ║${NC}"
        echo -e "${YELLOW}║  📊 Score: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)                                                    ║${NC}"
        echo -e "${YELLOW}║  🔧 Quelques ajustements recommandés                                        ║${NC}"
        echo -e "${YELLOW}║  🚀 Exécution possible avec prudence                                        ║${NC}"
        echo -e "${YELLOW}║                                                                              ║${NC}"
        echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    else
        echo -e "${RED}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${RED}║                                                                              ║${NC}"
        echo -e "${RED}║                    ❌ VALIDATION CRITIQUE - PRÉPARATION REQUISE ❌            ║${NC}"
        echo -e "${RED}║                                                                              ║${NC}"
        echo -e "${RED}║  📊 Score: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)                                                    ║${NC}"
        echo -e "${RED}║  🔧 Préparation supplémentaire nécessaire                                   ║${NC}"
        echo -e "${RED}║  ⏸️ Exécution reportée jusqu'à résolution                                    ║${NC}"
        echo -e "${RED}║                                                                              ║${NC}"
        echo -e "${RED}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    fi
    
    echo ""
    log "📄 Rapport détaillé: $VALIDATION_REPORT"
    log "🔧 Prochaine étape: Consulter le rapport pour actions spécifiques"
}

# Fonction principale
main() {
    header "🔍 VALIDATION FINALE - PRÊT POUR SPRINTS 18-20"
    
    log "Démarrage validation complète..."
    
    # Exécuter toutes les validations
    validate_sprint17_prerequisite
    validate_sprint18_readiness
    validate_sprint19_readiness
    validate_sprint20_readiness
    validate_scripts_and_tools
    validate_hanuman_infrastructure
    validate_technical_environment
    
    # Générer rapport et afficher résultats
    generate_readiness_report
    show_final_results
    
    success "🎉 Validation terminée avec succès !"
}

# Exécution du script
main "$@"
