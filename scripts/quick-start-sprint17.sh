#!/bin/bash

# 🚀 DÉMARRAGE RAPIDE SPRINT 17 - MOBILE-FIRST REVOLUTION
# Script d'exécution immédiate pour continuer l'implémentation

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions utilitaires
log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; exit 1; }

header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║$(printf "%*s" $(((78-${#1})/2)) "")${1}$(printf "%*s" $(((78-${#1})/2)) "")║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Détection de l'état actuel
detect_current_state() {
    header "🔍 DÉTECTION ÉTAT ACTUEL"
    
    log "Analyse de l'état du Sprint 17..."
    
    # Vérifier si Sprint 17 a été démarré
    if [[ -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/public/sw.js" ]]; then
        if [[ -f "$PROJECT_ROOT/Front-Audrey-V1-Main-main/vite.config.mobile.ts" ]]; then
            if [[ -d "$PROJECT_ROOT/Front-Audrey-V1-Main-main/src/mobile/components" ]]; then
                echo -e "${GREEN}📱 Sprint 17 Jour 3+ détecté${NC}"
                return 3
            else
                echo -e "${YELLOW}⚡ Sprint 17 Jour 2 détecté${NC}"
                return 2
            fi
        else
            echo -e "${CYAN}🚀 Sprint 17 Jour 1 détecté${NC}"
            return 1
        fi
    else
        echo -e "${BLUE}📋 Sprint 17 pas encore démarré${NC}"
        return 0
    fi
}

# Exécution selon l'état
execute_based_on_state() {
    local state=$1
    
    case $state in
        0)
            header "🚀 DÉMARRAGE SPRINT 17 - JOUR 1"
            log "Exécution du démarrage complet..."
            
            if [[ -x "$PROJECT_ROOT/scripts/start-sprint17-mobile-revolution.sh" ]]; then
                "$PROJECT_ROOT/scripts/start-sprint17-mobile-revolution.sh"
                success "Sprint 17 Jour 1 démarré avec succès !"
            else
                error "Script de démarrage Sprint 17 non trouvé ou non exécutable"
            fi
            ;;
            
        1)
            header "⚡ CONTINUATION SPRINT 17 - JOUR 2"
            log "Exécution optimisations performance mobile..."
            
            if [[ -x "$PROJECT_ROOT/scripts/continue-sprint17-day2.sh" ]]; then
                "$PROJECT_ROOT/scripts/continue-sprint17-day2.sh"
                success "Sprint 17 Jour 2 complété avec succès !"
            else
                error "Script Jour 2 Sprint 17 non trouvé ou non exécutable"
            fi
            ;;
            
        2)
            header "🎨 CONTINUATION SPRINT 17 - JOUR 3"
            log "Création du Design System mobile-first..."
            
            # Créer le script Jour 3 s'il n'existe pas
            if [[ ! -f "$PROJECT_ROOT/scripts/continue-sprint17-day3.sh" ]]; then
                log "Création du script Jour 3..."
                create_day3_script
            fi
            
            if [[ -x "$PROJECT_ROOT/scripts/continue-sprint17-day3.sh" ]]; then
                "$PROJECT_ROOT/scripts/continue-sprint17-day3.sh"
                success "Sprint 17 Jour 3 complété avec succès !"
            else
                error "Script Jour 3 Sprint 17 non exécutable"
            fi
            ;;
            
        3)
            header "📱 SPRINT 17 - ÉTAT AVANCÉ"
            log "Sprint 17 déjà bien avancé, vérification de l'état..."
            
            # Validation de l'état actuel
            if [[ -x "$PROJECT_ROOT/scripts/validate-current-sprint-status.sh" ]]; then
                "$PROJECT_ROOT/scripts/validate-current-sprint-status.sh"
            fi
            
            success "Validation de l'état Sprint 17 complétée"
            ;;
    esac
}

# Création du script Jour 3 (si nécessaire)
create_day3_script() {
    cat > "$PROJECT_ROOT/scripts/continue-sprint17-day3.sh" << 'EOF'
#!/bin/bash

# 🎨 SPRINT 17 JOUR 3 - DESIGN SYSTEM MOBILE-FIRST
# Date: 9 Juin 2025
# Objectif: Créer Design System mobile-first et composants tactiles

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/Front-Audrey-V1-Main-main"

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }

header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║$(printf "%*s" $(((78-${#1})/2)) "")${1}$(printf "%*s" $(((78-${#1})/2)) "")║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Création des composants mobile
create_mobile_components() {
    header "🎨 CRÉATION COMPOSANTS MOBILE"
    
    mkdir -p "$FRONTEND_DIR/src/mobile/components"
    
    # Composant Button mobile-first
    cat > "$FRONTEND_DIR/src/mobile/components/MobileButton.tsx" << 'COMPONENT_EOF'
import React from 'react';
import { motion } from 'framer-motion';

interface MobileButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export const MobileButton: React.FC<MobileButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  className = ''
}) => {
  const baseClasses = `
    relative inline-flex items-center justify-center
    font-medium rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    active:scale-95 select-none
    ${fullWidth ? 'w-full' : ''}
    ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
  `;

  const variantClasses = {
    primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-50 focus:ring-indigo-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[40px]',
    md: 'px-4 py-3 text-base min-h-[48px]',
    lg: 'px-6 py-4 text-lg min-h-[56px]'
  };

  return (
    <motion.button
      whileTap={{ scale: disabled || loading ? 1 : 0.95 }}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={disabled || loading ? undefined : onClick}
      disabled={disabled || loading}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </motion.button>
  );
};
COMPONENT_EOF

    success "Composants mobile créés"
}

# Fonction principale
main() {
    header "🎨 SPRINT 17 JOUR 3 - DESIGN SYSTEM MOBILE"
    
    create_mobile_components
    
    success "🎉 Jour 3 complété avec succès !"
}

main "$@"
EOF

    chmod +x "$PROJECT_ROOT/scripts/continue-sprint17-day3.sh"
    success "Script Jour 3 créé et rendu exécutable"
}

# Affichage du statut final
show_final_status() {
    header "📊 STATUT FINAL"
    
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                                                                              ║${NC}"
    echo -e "${CYAN}║                    🎉 SPRINT 17 - ACTIONS EXÉCUTÉES ! 🎉                    ║${NC}"
    echo -e "${CYAN}║                                                                              ║${NC}"
    echo -e "${CYAN}║  📱 Mobile-First Revolution en cours                                         ║${NC}"
    echo -e "${CYAN}║  ⚡ Performance <100ms en développement                                      ║${NC}"
    echo -e "${CYAN}║  🎨 Design System mobile-first                                              ║${NC}"
    echo -e "${CYAN}║  🚀 PWA Foundation opérationnelle                                           ║${NC}"
    echo -e "${CYAN}║                                                                              ║${NC}"
    echo -e "${CYAN}║  🎯 Objectif: Leadership mobile absolu                                      ║${NC}"
    echo -e "${CYAN}║  📈 Progression: Continue selon roadmap                                     ║${NC}"
    echo -e "${CYAN}║                                                                              ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    log "📋 Prochaines étapes disponibles:"
    log "   • Continuer Sprint 17 selon planning"
    log "   • Valider état: ./scripts/validate-current-sprint-status.sh"
    log "   • Préparer Sprint 18: Ecosystem Expansion"
}

# Fonction principale
main() {
    header "🚀 DÉMARRAGE RAPIDE SPRINT 17"
    
    log "Initialisation démarrage rapide..."
    
    # Détecter l'état actuel
    detect_current_state
    local current_state=$?
    
    # Exécuter selon l'état
    execute_based_on_state $current_state
    
    # Afficher le statut final
    show_final_status
    
    success "🎉 Démarrage rapide Sprint 17 terminé avec succès !"
}

# Exécution du script
main "$@"
