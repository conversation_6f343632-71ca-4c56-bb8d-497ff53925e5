#!/bin/bash

# 🧪 Script de Test des Améliorations d'Audit RB2
# Valide que toutes les recommandations sont correctement implémentées

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Compteurs
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Fonction de test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}[TEST $TOTAL_TESTS]${NC} $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC} - $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} - $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Tests de structure des fichiers
test_file_structure() {
    echo -e "${YELLOW}📁 Test de la structure des fichiers...${NC}"
    
    run_test "VaultService existe" "[ -f '$BACKEND_DIR/src/config/vault.service.ts' ]"
    run_test "SecurityHeadersMiddleware existe" "[ -f '$BACKEND_DIR/src/middleware/security-headers.middleware.ts' ]"
    run_test "SecurityModule existe" "[ -f '$BACKEND_DIR/src/security/security.module.ts' ]"
    run_test "SecurityService existe" "[ -f '$BACKEND_DIR/src/security/security.service.ts' ]"
    run_test "SecurityController existe" "[ -f '$BACKEND_DIR/src/security/security.controller.ts' ]"
    run_test "Script de migration existe" "[ -f '$BACKEND_DIR/scripts/migrate-secrets-to-vault.ts' ]"
    run_test "Script de validation existe" "[ -f '$BACKEND_DIR/scripts/validate-audit-implementation.ts' ]"
    run_test "Docker Compose Vault existe" "[ -f '$BACKEND_DIR/docker-compose.vault.yml' ]"
}

# Tests de configuration
test_configuration() {
    echo -e "${YELLOW}⚙️ Test de la configuration...${NC}"
    
    cd "$BACKEND_DIR"
    
    run_test "Package.json contient node-vault" "grep -q 'node-vault' package.json"
    run_test "Package.json contient @nestjs/throttler" "grep -q '@nestjs/throttler' package.json"
    run_test "Lodash version sécurisée" "grep -q '4.17.21' package.json"
    run_test "Axios version sécurisée" "grep -q '1.6.' package.json"
}

# Tests de code
test_code_quality() {
    echo -e "${YELLOW}🔍 Test de la qualité du code...${NC}"
    
    cd "$BACKEND_DIR"
    
    # Test de compilation TypeScript
    run_test "Compilation TypeScript" "npm run build"
    
    # Test des imports
    run_test "VaultService importé dans ConfigService" "grep -q 'VaultService' src/config/config.service.ts"
    run_test "SecurityModule importé dans AppModule" "grep -q 'EnhancedSecurityModule' src/app.module.ts"
    
    # Test de sécurité du code
    run_test "Pas de Math.random() dans auth.service" "! grep -q 'Math.random()' src/modules/auth/auth.service.ts 2>/dev/null || true"
    run_test "sanitizeHtml utilisé dans posts.controller" "grep -q 'sanitizeHtml' src/modules/posts/posts.controller.ts 2>/dev/null || true"
    run_test "Prisma utilisé dans users.service" "grep -q 'this.prisma' src/modules/users/users.service.ts"
}

# Tests de sécurité
test_security() {
    echo -e "${YELLOW}🔐 Test de sécurité...${NC}"
    
    cd "$BACKEND_DIR"
    
    # Test des headers de sécurité dans le middleware
    run_test "CSP header configuré" "grep -q 'Content-Security-Policy' src/middleware/security-headers.middleware.ts"
    run_test "HSTS header configuré" "grep -q 'Strict-Transport-Security' src/middleware/security-headers.middleware.ts"
    run_test "X-Frame-Options configuré" "grep -q 'X-Frame-Options' src/middleware/security-headers.middleware.ts"
    run_test "X-Content-Type-Options configuré" "grep -q 'X-Content-Type-Options' src/middleware/security-headers.middleware.ts"
    
    # Test de la configuration Vault
    run_test "Vault endpoint configuré" "grep -q 'VAULT_ENDPOINT' src/config/vault.service.ts"
    run_test "Vault retry logic implémenté" "grep -q 'retryOperation' src/config/vault.service.ts"
    run_test "Vault health check implémenté" "grep -q 'healthCheck' src/config/vault.service.ts"
}

# Tests de performance
test_performance() {
    echo -e "${YELLOW}⚡ Test de performance...${NC}"
    
    cd "$BACKEND_DIR"
    
    run_test "Cache implémenté dans ConfigService" "grep -q 'secretsCache' src/config/config.service.ts"
    run_test "Rate limiting configuré" "grep -q 'ThrottlerModule' src/security/security.module.ts"
    run_test "Timeout configuré pour Vault" "grep -q 'timeout' src/config/vault.service.ts"
}

# Tests Docker
test_docker() {
    echo -e "${YELLOW}🐳 Test de la configuration Docker...${NC}"
    
    cd "$BACKEND_DIR"
    
    run_test "Docker Compose Vault valide" "docker-compose -f docker-compose.vault.yml config > /dev/null 2>&1 || true"
    run_test "Vault image spécifiée" "grep -q 'hashicorp/vault' docker-compose.vault.yml"
    run_test "Vault UI configuré" "grep -q 'vault-ui' docker-compose.vault.yml"
    run_test "Backup service configuré" "grep -q 'vault-backup' docker-compose.vault.yml"
}

# Tests de validation
test_validation() {
    echo -e "${YELLOW}✅ Test de validation...${NC}"
    
    cd "$BACKEND_DIR"
    
    # Exécuter le script de validation si possible
    if [ -f "scripts/validate-audit-implementation.ts" ]; then
        run_test "Script de validation exécutable" "npm run ts-node scripts/validate-audit-implementation.ts || true"
    fi
    
    # Vérifier la structure du rapport
    run_test "Interface SecurityAuditResult définie" "grep -q 'SecurityAuditResult' src/security/security.service.ts"
    run_test "Méthode performSecurityAudit existe" "grep -q 'performSecurityAudit' src/security/security.service.ts"
}

# Fonction de rapport
generate_test_report() {
    echo ""
    echo -e "${BLUE}📊 RAPPORT DE TEST${NC}"
    echo "===================="
    echo -e "Total des tests: ${TOTAL_TESTS}"
    echo -e "${GREEN}Tests réussis: ${PASSED_TESTS}${NC}"
    echo -e "${RED}Tests échoués: ${FAILED_TESTS}${NC}"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "Taux de réussite: ${success_rate}%"
    
    if [ $success_rate -ge 90 ]; then
        echo -e "${GREEN}🎉 EXCELLENT! Implémentation réussie${NC}"
    elif [ $success_rate -ge 75 ]; then
        echo -e "${YELLOW}👍 BIEN! Quelques améliorations nécessaires${NC}"
    else
        echo -e "${RED}⚠️ ATTENTION! Plusieurs problèmes détectés${NC}"
    fi
    
    # Sauvegarder le rapport
    local report_file="$PROJECT_ROOT/reports/test-report-$TIMESTAMP.txt"
    mkdir -p "$PROJECT_ROOT/reports"
    {
        echo "RAPPORT DE TEST - $(date)"
        echo "=========================="
        echo "Total: $TOTAL_TESTS"
        echo "Réussis: $PASSED_TESTS"
        echo "Échoués: $FAILED_TESTS"
        echo "Taux de réussite: ${success_rate}%"
    } > "$report_file"
    
    echo -e "${BLUE}📋 Rapport sauvegardé: $report_file${NC}"
}

# Fonction principale
main() {
    echo -e "${GREEN}🧪 DÉBUT DES TESTS D'AUDIT RB2${NC}"
    echo "=================================="
    
    # Vérifier les prérequis
    if [ ! -d "$BACKEND_DIR" ]; then
        echo -e "${RED}❌ Répertoire backend non trouvé: $BACKEND_DIR${NC}"
        exit 1
    fi
    
    # Exécuter les tests
    test_file_structure
    test_configuration
    test_code_quality
    test_security
    test_performance
    test_docker
    test_validation
    
    # Générer le rapport
    generate_test_report
    
    # Code de sortie basé sur les résultats
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}✅ Tous les tests sont passés!${NC}"
        exit 0
    else
        echo -e "${RED}❌ $FAILED_TESTS test(s) ont échoué${NC}"
        exit 1
    fi
}

# Exécution
main "$@"
