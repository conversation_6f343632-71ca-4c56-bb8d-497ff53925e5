version: '3.8'

services:
  vault:
    image: hashicorp/vault:1.15.2
    container_name: rb2-vault
    restart: unless-stopped
    ports:
      - "8200:8200"
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_ROOT_TOKEN:-rb2-dev-token}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
      VAULT_ADDR: http://0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault-data:/vault/data
      - vault-logs:/vault/logs
      - ./vault-config:/vault/config
    command: >
      sh -c "
        vault server -dev -dev-root-token-id=${VAULT_ROOT_TOKEN:-rb2-dev-token} -dev-listen-address=0.0.0.0:8200 &
        sleep 5 &&
        export VAULT_ADDR=http://localhost:8200 &&
        export VAULT_TOKEN=${VAULT_ROOT_TOKEN:-rb2-dev-token} &&
        vault auth enable userpass &&
        vault policy write rb2-policy - <<EOF
        path \"rb2/*\" {
          capabilities = [\"create\", \"read\", \"update\", \"delete\", \"list\"]
        }
        path \"auth/token/lookup-self\" {
          capabilities = [\"read\"]
        }
        EOF
        vault secrets enable -path=rb2 kv-v2 &&
        vault write auth/userpass/users/rb2-admin password=${VAULT_ADMIN_PASSWORD:-admin123} policies=rb2-policy &&
        echo 'Vault setup completed' &&
        wait
      "
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - rb2-network

  vault-ui:
    image: djenriquez/vault-ui:latest
    container_name: rb2-vault-ui
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      VAULT_URL_DEFAULT: http://vault:8200
      VAULT_AUTH_DEFAULT: USERPASS
    depends_on:
      vault:
        condition: service_healthy
    networks:
      - rb2-network

  # Vault backup service
  vault-backup:
    image: alpine:latest
    container_name: rb2-vault-backup
    restart: unless-stopped
    volumes:
      - vault-data:/vault/data:ro
      - ./backups:/backups
    environment:
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM
    command: >
      sh -c "
        apk add --no-cache dcron &&
        echo '${BACKUP_SCHEDULE} tar -czf /backups/vault-backup-$(date +%Y%m%d-%H%M%S).tar.gz -C /vault/data .' | crontab - &&
        crond -f
      "
    depends_on:
      - vault
    networks:
      - rb2-network

volumes:
  vault-data:
    driver: local
  vault-logs:
    driver: local

networks:
  rb2-network:
    driver: bridge
    external: false
