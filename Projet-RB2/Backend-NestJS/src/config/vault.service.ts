import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as vault from 'node-vault';

interface VaultConfig {
  endpoint: string;
  token: string;
  namespace?: string;
  timeout: number;
  retries: number;
}

interface SecretData {
  [key: string]: any;
}

@Injectable()
export class VaultService implements OnModuleInit {
  private readonly logger = new Logger(VaultService.name);
  private vaultClient: any;
  private isInitialized = false;
  private config: VaultConfig;

  constructor() {
    this.config = {
      endpoint: process.env.VAULT_ENDPOINT || 'http://localhost:8200',
      token: process.env.VAULT_TOKEN,
      namespace: process.env.VAULT_NAMESPACE,
      timeout: parseInt(process.env.VAULT_TIMEOUT || '5000'),
      retries: parseInt(process.env.VAULT_RETRIES || '3'),
    };
  }

  async onModuleInit() {
    await this.initializeVault();
  }

  private async initializeVault(): Promise<void> {
    try {
      this.vaultClient = vault({
        apiVersion: 'v1',
        endpoint: this.config.endpoint,
        token: this.config.token,
        namespace: this.config.namespace,
        timeout: this.config.timeout,
      });

      // Test connection with health check
      await this.healthCheck();
      this.isInitialized = true;
      this.logger.log('✅ Vault connection established successfully');
    } catch (error) {
      this.logger.error('❌ Failed to connect to Vault:', error.message);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const status = await this.vaultClient.status();
      return !status.sealed;
    } catch (error) {
      this.logger.error('Vault health check failed:', error.message);
      return false;
    }
  }

  async getSecret(path: string): Promise<SecretData> {
    if (!this.isInitialized) {
      throw new Error('Vault service not initialized');
    }

    try {
      const result = await this.retryOperation(async () => {
        return await this.vaultClient.read(`rb2/data/${path}`);
      });
      
      return result.data.data;
    } catch (error) {
      this.logger.error(`Failed to read secret from path: ${path}`, error.message);
      throw error;
    }
  }

  async setSecret(path: string, data: SecretData): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Vault service not initialized');
    }

    try {
      await this.retryOperation(async () => {
        return await this.vaultClient.write(`rb2/data/${path}`, { data });
      });
      
      this.logger.log(`✅ Secret written to path: ${path}`);
    } catch (error) {
      this.logger.error(`Failed to write secret to path: ${path}`, error.message);
      throw error;
    }
  }

  async deleteSecret(path: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Vault service not initialized');
    }

    try {
      await this.retryOperation(async () => {
        return await this.vaultClient.delete(`rb2/data/${path}`);
      });
      
      this.logger.log(`✅ Secret deleted from path: ${path}`);
    } catch (error) {
      this.logger.error(`Failed to delete secret from path: ${path}`, error.message);
      throw error;
    }
  }

  async listSecrets(path: string = ''): Promise<string[]> {
    if (!this.isInitialized) {
      throw new Error('Vault service not initialized');
    }

    try {
      const result = await this.retryOperation(async () => {
        return await this.vaultClient.list(`rb2/metadata/${path}`);
      });
      
      return result.data.keys || [];
    } catch (error) {
      this.logger.error(`Failed to list secrets from path: ${path}`, error.message);
      throw error;
    }
  }

  private async retryOperation<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.config.retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.logger.warn(`Vault operation failed (attempt ${attempt}/${this.config.retries}):`, error.message);
        
        if (attempt < this.config.retries) {
          await this.delay(1000 * attempt); // Exponential backoff
        }
      }
    }
    
    throw lastError;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods for common secret patterns
  async getDatabaseConfig(): Promise<any> {
    return await this.getSecret('database');
  }

  async getJwtConfig(): Promise<any> {
    return await this.getSecret('jwt');
  }

  async getExternalApiKeys(): Promise<any> {
    return await this.getSecret('external-apis');
  }

  async getEmailConfig(): Promise<any> {
    return await this.getSecret('email');
  }
}
