import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { VaultService } from './vault.service';

@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private secretsCache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(
    private configService: NestConfigService,
    private vaultService: VaultService
  ) {}

  get(key: string): string {
    return this.configService.get<string>(key);
  }

  getNumber(key: string): number {
    const value = this.get(key);
    return value ? parseInt(value, 10) : undefined;
  }

  getBoolean(key: string): boolean {
    const value = this.get(key);
    return value === 'true' || value === '1';
  }

  // Enhanced API Keys configuration with Vault fallback
  async getExternalApiConfig() {
    try {
      // Try to get from Vault first
      const vaultSecrets = await this.getFromVaultWithCache('external-apis');
      if (vaultSecrets) {
        return {
          recommendationApiKey: vaultSecrets.recommendationApiKey,
          weatherApiKey: vaultSecrets.weatherApiKey,
          mapApiKey: vaultSecrets.mapApiKey,
          analyticsApiKey: vaultSecrets.analyticsApiKey
        };
      }
    } catch (error) {
      this.logger.warn('Failed to get API keys from Vault, falling back to environment variables');
    }

    // Fallback to environment variables
    return {
      recommendationApiKey: this.get('RECOMMENDATION_API_KEY'),
      weatherApiKey: this.get('WEATHER_API_KEY'),
      mapApiKey: this.get('MAP_API_KEY'),
      analyticsApiKey: this.get('ANALYTICS_API_KEY')
    };
  }

  async getDatabaseConfig() {
    try {
      // Try to get from Vault first
      const vaultSecrets = await this.getFromVaultWithCache('database');
      if (vaultSecrets) {
        return {
          host: vaultSecrets.host,
          port: vaultSecrets.port,
          username: vaultSecrets.username,
          password: vaultSecrets.password,
          database: vaultSecrets.database,
        };
      }
    } catch (error) {
      this.logger.warn('Failed to get database config from Vault, falling back to environment variables');
    }

    // Fallback to environment variables
    return {
      host: this.get('DB_HOST'),
      port: this.getNumber('DB_PORT'),
      username: this.get('DB_USERNAME'),
      password: this.get('DB_PASSWORD'),
      database: this.get('DB_DATABASE'),
    };
  }

  async getJwtConfig() {
    try {
      // Try to get from Vault first
      const vaultSecrets = await this.getFromVaultWithCache('jwt');
      if (vaultSecrets) {
        return {
          secret: vaultSecrets.secret,
          expiresIn: vaultSecrets.expiresIn || '1d',
        };
      }
    } catch (error) {
      this.logger.warn('Failed to get JWT config from Vault, falling back to environment variables');
    }

    // Fallback to environment variables
    return {
      secret: this.get('JWT_SECRET'),
      expiresIn: this.get('JWT_EXPIRES_IN') || '1d',
    };
  }

  // Cache management for Vault secrets
  private async getFromVaultWithCache(path: string): Promise<any> {
    const cacheKey = `vault:${path}`;
    const now = Date.now();

    // Check if we have a valid cached value
    if (this.secretsCache.has(cacheKey)) {
      const expiry = this.cacheExpiry.get(cacheKey);
      if (expiry && now < expiry) {
        return this.secretsCache.get(cacheKey);
      }
    }

    // Fetch from Vault
    try {
      const secrets = await this.vaultService.getSecret(path);

      // Cache the result
      this.secretsCache.set(cacheKey, secrets);
      this.cacheExpiry.set(cacheKey, now + this.CACHE_TTL);

      return secrets;
    } catch (error) {
      // If we have a cached value (even expired), use it as fallback
      if (this.secretsCache.has(cacheKey)) {
        this.logger.warn(`Using expired cache for ${path} due to Vault error`);
        return this.secretsCache.get(cacheKey);
      }
      throw error;
    }
  }

  // Clear cache method for testing or manual refresh
  clearSecretsCache(): void {
    this.secretsCache.clear();
    this.cacheExpiry.clear();
    this.logger.log('Secrets cache cleared');
  }

  getAppConfig() {
    return {
      port: this.getNumber('PORT') || 3000,
      environment: this.get('NODE_ENV') || 'development',
      apiPrefix: this.get('API_PREFIX') || 'api',
      appName: this.get('APP_NAME') || 'Retreat And Be API',
    };
  }
}
