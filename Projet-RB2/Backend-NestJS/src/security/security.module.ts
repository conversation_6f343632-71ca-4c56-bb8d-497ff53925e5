import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { VaultService } from '../config/vault.service';
import { SecurityHeadersMiddleware, ApiSecurityMiddleware, RateLimitHeadersMiddleware } from '../middleware/security-headers.middleware';
import { SecurityService } from './security.service';
import { SecurityController } from './security.controller';

@Module({
  imports: [
    ConfigModule,
    // Rate limiting configuration
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 1000, // 1 second
        limit: 10, // 10 requests per second
      },
      {
        name: 'medium',
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
      {
        name: 'long',
        ttl: 900000, // 15 minutes
        limit: 1000, // 1000 requests per 15 minutes
      },
    ]),
  ],
  providers: [
    VaultService,
    SecurityService,
  ],
  controllers: [SecurityController],
  exports: [VaultService, SecurityService],
})
export class SecurityModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply security headers to all routes
    consumer
      .apply(SecurityHeadersMiddleware)
      .forRoutes('*');

    // Apply API-specific security to API routes
    consumer
      .apply(ApiSecurityMiddleware)
      .forRoutes('/api/*');

    // Apply rate limit headers
    consumer
      .apply(RateLimitHeadersMiddleware)
      .forRoutes('*');
  }
}
