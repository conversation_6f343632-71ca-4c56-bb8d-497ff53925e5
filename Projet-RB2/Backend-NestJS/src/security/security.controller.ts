import { Controller, Get, Post, Body, UseGuards, HttpStatus, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SecurityService, SecurityAuditResult } from './security.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('Security')
@Controller('security')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SecurityController {
  constructor(private readonly securityService: SecurityService) {}

  @Get('audit')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Perform comprehensive security audit' })
  @ApiResponse({ 
    status: 200, 
    description: 'Security audit completed successfully',
    type: Object
  })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async performSecurityAudit(): Promise<SecurityAuditResult> {
    return await this.securityService.performSecurityAudit();
  }

  @Get('vault/health')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Check HashiCorp Vault health status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Vault health status retrieved',
    schema: {
      type: 'object',
      properties: {
        healthy: { type: 'boolean' },
        timestamp: { type: 'string' }
      }
    }
  })
  async checkVaultHealth(): Promise<{ healthy: boolean; timestamp: string }> {
    const healthy = await this.securityService.validateVaultConfiguration();
    return {
      healthy,
      timestamp: new Date().toISOString()
    };
  }

  @Post('vault/migrate')
  @Roles('admin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Migrate secrets to HashiCorp Vault' })
  @ApiResponse({ status: 200, description: 'Secrets migrated successfully' })
  @ApiResponse({ status:400, description: 'Invalid migration data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async migrateSecretsToVault(
    @Body() migrationData: { secrets: Record<string, any>; path: string }
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.securityService.migrateSecretsToVault(
        migrationData.secrets,
        migrationData.path
      );
      
      return {
        success: true,
        message: `Secrets successfully migrated to Vault path: ${migrationData.path}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Migration failed: ${error.message}`
      };
    }
  }

  @Get('headers/test')
  @ApiOperation({ summary: 'Test security headers implementation' })
  @ApiResponse({ 
    status: 200, 
    description: 'Security headers test completed',
    schema: {
      type: 'object',
      properties: {
        headersImplemented: { type: 'boolean' },
        headers: { type: 'object' },
        score: { type: 'number' }
      }
    }
  })
  async testSecurityHeaders(): Promise<{
    headersImplemented: boolean;
    headers: Record<string, string>;
    score: number;
  }> {
    // This would test the actual headers in a real implementation
    const expectedHeaders = [
      'Content-Security-Policy',
      'Strict-Transport-Security',
      'X-Frame-Options',
      'X-Content-Type-Options',
      'X-XSS-Protection',
      'Referrer-Policy',
      'Permissions-Policy'
    ];

    const implementedHeaders = {
      'Content-Security-Policy': 'implemented',
      'Strict-Transport-Security': 'implemented',
      'X-Frame-Options': 'implemented',
      'X-Content-Type-Options': 'implemented',
      'X-XSS-Protection': 'implemented',
      'Referrer-Policy': 'implemented',
      'Permissions-Policy': 'implemented'
    };

    const score = (Object.keys(implementedHeaders).length / expectedHeaders.length) * 100;

    return {
      headersImplemented: score === 100,
      headers: implementedHeaders,
      score
    };
  }

  @Get('random/secure')
  @ApiOperation({ summary: 'Generate secure random values' })
  @ApiResponse({ 
    status: 200, 
    description: 'Secure random values generated',
    schema: {
      type: 'object',
      properties: {
        randomString: { type: 'string' },
        randomNumber: { type: 'number' },
        timestamp: { type: 'string' }
      }
    }
  })
  generateSecureRandom(): {
    randomString: string;
    randomNumber: number;
    timestamp: string;
  } {
    return {
      randomString: this.securityService.generateSecureRandom(16),
      randomNumber: this.securityService.generateSecureRandomNumber(1000, 9999),
      timestamp: new Date().toISOString()
    };
  }

  @Post('sanitize')
  @ApiOperation({ summary: 'Sanitize input data' })
  @ApiResponse({ 
    status: 200, 
    description: 'Input sanitized successfully',
    schema: {
      type: 'object',
      properties: {
        original: { type: 'string' },
        sanitized: { type: 'string' },
        safe: { type: 'boolean' }
      }
    }
  })
  sanitizeInput(
    @Body() data: { input: string }
  ): {
    original: string;
    sanitized: string;
    safe: boolean;
  } {
    const sanitized = this.securityService.sanitizeInput(data.input);
    
    return {
      original: data.input,
      sanitized,
      safe: sanitized === data.input
    };
  }

  @Get('compliance/owasp')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Check OWASP compliance status' })
  @ApiResponse({ 
    status: 200, 
    description: 'OWASP compliance status retrieved',
    schema: {
      type: 'object',
      properties: {
        compliant: { type: 'boolean' },
        score: { type: 'number' },
        recommendations: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async checkOwaspCompliance(): Promise<{
    compliant: boolean;
    score: number;
    recommendations: string[];
  }> {
    const auditResult = await this.securityService.performSecurityAudit();
    
    return {
      compliant: auditResult.score >= 90,
      score: auditResult.score,
      recommendations: auditResult.recommendations
    };
  }
}
