#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { VaultService } from '../src/config/vault.service';
import { SecurityService } from '../src/security/security.service';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface SecretMigration {
  path: string;
  secrets: Record<string, any>;
  source: string;
}

class SecretsMigrationScript {
  private readonly logger = new Logger(SecretsMigrationScript.name);
  private vaultService: VaultService;
  private securityService: SecurityService;

  constructor() {
    this.vaultService = new VaultService();
    this.securityService = new SecurityService(this.vaultService);
  }

  async run(): Promise<void> {
    try {
      this.logger.log('🚀 Starting secrets migration to HashiCorp Vault...');

      // Initialize Vault service
      await this.vaultService.onModuleInit();

      // Validate Vault configuration
      const isVaultValid = await this.securityService.validateVaultConfiguration();
      if (!isVaultValid) {
        throw new Error('Vault configuration validation failed');
      }

      // Prepare secrets for migration
      const migrations = await this.prepareSecretsMigrations();

      // Perform migrations
      await this.performMigrations(migrations);

      // Validate migrations
      await this.validateMigrations(migrations);

      // Generate migration report
      await this.generateMigrationReport(migrations);

      this.logger.log('✅ Secrets migration completed successfully!');

    } catch (error) {
      this.logger.error(`❌ Migration failed: ${error.message}`);
      process.exit(1);
    }
  }

  private async prepareSecretsMigrations(): Promise<SecretMigration[]> {
    const migrations: SecretMigration[] = [];

    // Database secrets
    migrations.push({
      path: 'database',
      secrets: {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
        database: process.env.DB_DATABASE || 'retreat_and_be',
        ssl: process.env.DB_SSL === 'true',
        connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10')
      },
      source: 'Environment Variables'
    });

    // JWT secrets
    migrations.push({
      path: 'jwt',
      secrets: {
        secret: process.env.JWT_SECRET || this.securityService.generateSecureRandom(64),
        expiresIn: process.env.JWT_EXPIRES_IN || '1d',
        refreshSecret: process.env.JWT_REFRESH_SECRET || this.securityService.generateSecureRandom(64),
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
      },
      source: 'Environment Variables'
    });

    // External API keys
    migrations.push({
      path: 'external-apis',
      secrets: {
        recommendationApiKey: process.env.RECOMMENDATION_API_KEY || '',
        weatherApiKey: process.env.WEATHER_API_KEY || '',
        mapApiKey: process.env.MAP_API_KEY || '',
        analyticsApiKey: process.env.ANALYTICS_API_KEY || '',
        stripeSecretKey: process.env.STRIPE_SECRET_KEY || '',
        sendgridApiKey: process.env.SENDGRID_API_KEY || ''
      },
      source: 'Environment Variables'
    });

    // Email configuration
    migrations.push({
      path: 'email',
      secrets: {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true',
        username: process.env.EMAIL_USERNAME || '',
        password: process.env.EMAIL_PASSWORD || '',
        from: process.env.EMAIL_FROM || '<EMAIL>'
      },
      source: 'Environment Variables'
    });

    // Redis configuration
    migrations.push({
      path: 'redis',
      secrets: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || '',
        db: parseInt(process.env.REDIS_DB || '0'),
        ttl: parseInt(process.env.REDIS_TTL || '3600')
      },
      source: 'Environment Variables'
    });

    // Application secrets
    migrations.push({
      path: 'application',
      secrets: {
        encryptionKey: process.env.ENCRYPTION_KEY || this.securityService.generateSecureRandom(32),
        sessionSecret: process.env.SESSION_SECRET || this.securityService.generateSecureRandom(32),
        csrfSecret: process.env.CSRF_SECRET || this.securityService.generateSecureRandom(32),
        apiSecret: process.env.API_SECRET || this.securityService.generateSecureRandom(32)
      },
      source: 'Environment Variables'
    });

    return migrations;
  }

  private async performMigrations(migrations: SecretMigration[]): Promise<void> {
    this.logger.log(`📦 Migrating ${migrations.length} secret groups to Vault...`);

    for (const migration of migrations) {
      try {
        this.logger.log(`🔄 Migrating secrets to path: ${migration.path}`);
        
        // Filter out empty secrets
        const filteredSecrets = Object.fromEntries(
          Object.entries(migration.secrets).filter(([_, value]) => value !== '')
        );

        if (Object.keys(filteredSecrets).length === 0) {
          this.logger.warn(`⚠️  No valid secrets found for path: ${migration.path}`);
          continue;
        }

        await this.securityService.migrateSecretsToVault(filteredSecrets, migration.path);
        this.logger.log(`✅ Successfully migrated ${Object.keys(filteredSecrets).length} secrets to ${migration.path}`);

      } catch (error) {
        this.logger.error(`❌ Failed to migrate secrets to ${migration.path}: ${error.message}`);
        throw error;
      }
    }
  }

  private async validateMigrations(migrations: SecretMigration[]): Promise<void> {
    this.logger.log('🔍 Validating migrated secrets...');

    for (const migration of migrations) {
      try {
        const retrievedSecrets = await this.vaultService.getSecret(migration.path);
        
        if (!retrievedSecrets) {
          throw new Error(`No secrets found at path: ${migration.path}`);
        }

        this.logger.log(`✅ Validation successful for path: ${migration.path}`);

      } catch (error) {
        this.logger.error(`❌ Validation failed for path: ${migration.path}: ${error.message}`);
        throw error;
      }
    }
  }

  private async generateMigrationReport(migrations: SecretMigration[]): Promise<void> {
    const report = {
      timestamp: new Date().toISOString(),
      totalMigrations: migrations.length,
      successfulMigrations: migrations.length,
      failedMigrations: 0,
      migrations: migrations.map(m => ({
        path: m.path,
        secretCount: Object.keys(m.secrets).length,
        source: m.source,
        status: 'success'
      })),
      recommendations: [
        'Update application configuration to use Vault service',
        'Remove sensitive environment variables from .env files',
        'Implement secret rotation policies',
        'Set up Vault monitoring and alerting',
        'Configure Vault backup and disaster recovery'
      ]
    };

    const reportPath = path.join(__dirname, '../reports/vault-migration-report.json');
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.logger.log(`📊 Migration report generated: ${reportPath}`);
  }
}

// Run the migration script
if (require.main === module) {
  const migrationScript = new SecretsMigrationScript();
  migrationScript.run().catch(error => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
}

export { SecretsMigrationScript };
