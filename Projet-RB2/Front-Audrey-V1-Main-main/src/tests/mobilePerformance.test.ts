import { performance } from 'perf_hooks';

// Test de performance navigation
export const testNavigationPerformance = async () => {
  const startTime = performance.now();
  
  // Simuler navigation
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`Navigation time: ${duration.toFixed(2)}ms`);
  
  // Objectif: <100ms
  expect(duration).toBeLessThan(100);
  
  return duration;
};

// Test de taille de bundle
export const testBundleSize = async () => {
  const response = await fetch('/assets/index.js');
  const size = parseInt(response.headers.get('content-length') || '0');
  const sizeKB = size / 1024;
  
  console.log(`Bundle size: ${sizeKB.toFixed(2)}KB`);
  
  // Objectif: <500KB
  expect(sizeKB).toBeLessThan(500);
  
  return sizeKB;
};

// Test de Core Web Vitals
export const testCoreWebVitals = () => {
  return new Promise((resolve) => {
    const vitals = {
      LCP: 0,
      FID: 0,
      CLS: 0
    };
    
    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      vitals.LCP = lastEntry.startTime;
      console.log(`LCP: ${vitals.LCP.toFixed(2)}ms`);
    }).observe({ entryTypes: ['largest-contentful-paint'] });
    
    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        vitals.FID = entry.processingStart - entry.startTime;
        console.log(`FID: ${vitals.FID.toFixed(2)}ms`);
      });
    }).observe({ entryTypes: ['first-input'] });
    
    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          vitals.CLS += entry.value;
        }
      });
      console.log(`CLS: ${vitals.CLS.toFixed(3)}`);
    }).observe({ entryTypes: ['layout-shift'] });
    
    setTimeout(() => resolve(vitals), 5000);
  });
};

// Test de cache hit ratio
export const testCacheHitRatio = async () => {
  const cacheNames = await caches.keys();
  let totalRequests = 0;
  let cacheHits = 0;
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    totalRequests += requests.length;
    
    for (const request of requests) {
      const response = await cache.match(request);
      if (response) cacheHits++;
    }
  }
  
  const hitRatio = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
  console.log(`Cache hit ratio: ${hitRatio.toFixed(2)}%`);
  
  // Objectif: >90%
  expect(hitRatio).toBeGreaterThan(90);
  
  return hitRatio;
};

// Suite de tests complète
export const runMobilePerformanceTests = async () => {
  console.log('🚀 Démarrage tests performance mobile...');
  
  const results = {
    navigation: await testNavigationPerformance(),
    bundleSize: await testBundleSize(),
    webVitals: await testCoreWebVitals(),
    cacheRatio: await testCacheHitRatio()
  };
  
  console.log('📊 Résultats tests performance:', results);
  
  return results;
};
