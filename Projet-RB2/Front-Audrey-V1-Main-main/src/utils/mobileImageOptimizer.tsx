import { useState, useEffect, useRef } from 'react';

interface ImageOptimizerProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

// Hook pour détection du support WebP/AVIF
const useImageFormatSupport = () => {
  const [supports, setSupports] = useState({
    webp: false,
    avif: false
  });

  useEffect(() => {
    const checkWebP = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    };

    const checkAVIF = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
    };

    setSupports({
      webp: checkWebP(),
      avif: checkAVIF()
    });
  }, []);

  return supports;
};

// Générateur d'URLs optimisées
const generateOptimizedUrl = (
  src: string,
  width: number,
  quality = 80,
  format?: 'webp' | 'avif' | 'jpeg'
) => {
  // Ici vous pouvez intégrer avec votre service d'optimisation d'images
  // (Cloudinary, ImageKit, etc.)
  const params = new URLSearchParams({
    w: width.toString(),
    q: quality.toString(),
    ...(format && { f: format })
  });
  
  return `${src}?${params.toString()}`;
};

// Composant Image optimisé pour mobile
export const MobileOptimizedImage: React.FC<ImageOptimizerProps> = ({
  src,
  alt,
  className = '',
  sizes = '100vw',
  loading = 'lazy',
  priority = false,
  quality = 80,
  placeholder = 'empty',
  blurDataURL
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const { webp, avif } = useImageFormatSupport();

  // Générer les sources pour différents formats
  const generateSources = () => {
    const breakpoints = [640, 768, 1024, 1280, 1536];
    const sources = [];

    // AVIF (meilleure compression)
    if (avif) {
      const avifSrcSet = breakpoints
        .map(bp => `${generateOptimizedUrl(src, bp, quality, 'avif')} ${bp}w`)
        .join(', ');
      sources.push(
        <source key="avif" type="image/avif" srcSet={avifSrcSet} sizes={sizes} />
      );
    }

    // WebP (bon support)
    if (webp) {
      const webpSrcSet = breakpoints
        .map(bp => `${generateOptimizedUrl(src, bp, quality, 'webp')} ${bp}w`)
        .join(', ');
      sources.push(
        <source key="webp" type="image/webp" srcSet={webpSrcSet} sizes={sizes} />
      );
    }

    return sources;
  };

  // Intersection Observer pour lazy loading
  useEffect(() => {
    if (loading === 'lazy' && !priority && imgRef.current) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      observer.observe(imgRef.current);
      return () => observer.disconnect();
    }
  }, [loading, priority]);

  const handleLoad = () => setIsLoaded(true);
  const handleError = () => setError(true);

  if (error) {
    return (
      <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        <span className="text-gray-500 text-sm">Image non disponible</span>
      </div>
    );
  }

  return (
    <picture className={className}>
      {generateSources()}
      <img
        ref={imgRef}
        src={priority || loading === 'eager' ? src : undefined}
        data-src={loading === 'lazy' && !priority ? src : undefined}
        alt={alt}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        className={`
          transition-opacity duration-300
          ${isLoaded ? 'opacity-100' : 'opacity-0'}
          ${placeholder === 'blur' && !isLoaded ? 'blur-sm' : ''}
          ${className}
        `}
        style={{
          backgroundImage: placeholder === 'blur' && blurDataURL ? `url(${blurDataURL})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      />
    </picture>
  );
};

// Hook pour preload d'images critiques
export const useImagePreload = (urls: string[]) => {
  useEffect(() => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      document.head.appendChild(link);
    });
  }, [urls]);
};

// Composant pour images hero avec preload
export const MobileHeroImage: React.FC<ImageOptimizerProps> = (props) => {
  useImagePreload([props.src]);
  
  return (
    <MobileOptimizedImage
      {...props}
      priority={true}
      loading="eager"
      quality={90}
    />
  );
};

// Composant pour galeries avec lazy loading agressif
export const MobileGalleryImage: React.FC<ImageOptimizerProps> = (props) => {
  return (
    <MobileOptimizedImage
      {...props}
      loading="lazy"
      quality={75}
      placeholder="blur"
    />
  );
};
