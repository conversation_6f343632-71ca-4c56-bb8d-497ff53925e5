import { lazy, Suspense, ComponentType } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useState, useEffect, useRef } from 'react';

// Composant de fallback pour le loading
const LoadingFallback = ({ message = 'Chargement...' }: { message?: string }) => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
    <span className="ml-2 text-gray-600">{message}</span>
  </div>
);

// Composant d'erreur
const ErrorFallback = ({ error, resetErrorBoundary }: any) => (
  <div className="text-center p-4">
    <h2 className="text-lg font-semibold text-red-600 mb-2">
      Erreur de chargement
    </h2>
    <p className="text-gray-600 mb-4">{error.message}</p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
    >
      Réessayer
    </button>
  </div>
);

// HOC pour lazy loading avec error boundary
export const withLazyLoading = <P extends object>(
  Component: ComponentType<P>,
  fallbackMessage?: string
) => {
  const LazyComponent = lazy(() => Promise.resolve({ default: Component }));
  
  return (props: P) => (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback message={fallbackMessage} />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Lazy loading avec preload
export const createLazyComponent = <P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  fallbackMessage?: string
) => {
  const LazyComponent = lazy(importFn);
  
  // Fonction de preload
  const preload = () => importFn();
  
  const WrappedComponent = (props: P) => (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback message={fallbackMessage} />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
  
  WrappedComponent.preload = preload;
  return WrappedComponent;
};

// Lazy loading conditionnel basé sur la viewport
export const createViewportLazyComponent = <P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: IntersectionObserverInit = {}
) => {
  const LazyComponent = lazy(importFn);
  
  return (props: P) => {
    const [isVisible, setIsVisible] = useState(false);
    const ref = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1, ...options }
      );
      
      if (ref.current) {
        observer.observe(ref.current);
      }
      
      return () => observer.disconnect();
    }, []);
    
    return (
      <div ref={ref}>
        {isVisible ? (
          <ErrorBoundary FallbackComponent={ErrorFallback}>
            <Suspense fallback={<LoadingFallback />}>
              <LazyComponent {...props} />
            </Suspense>
          </ErrorBoundary>
        ) : (
          <div className="min-h-[200px] bg-gray-50 animate-pulse" />
        )}
      </div>
    );
  };
};

// Lazy loading avec retry
export const createRetryLazyComponent = <P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  maxRetries = 3
) => {
  const LazyComponent = lazy(() => {
    let retries = 0;
    
    const loadWithRetry = async (): Promise<{ default: ComponentType<P> }> => {
      try {
        return await importFn();
      } catch (error) {
        if (retries < maxRetries) {
          retries++;
          console.warn(`Retry ${retries}/${maxRetries} for lazy component`);
          await new Promise(resolve => setTimeout(resolve, 1000 * retries));
          return loadWithRetry();
        }
        throw error;
      }
    };
    
    return loadWithRetry();
  });
  
  return (props: P) => (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Hook pour preload conditionnel
export const useConditionalPreload = (
  preloadFn: () => void,
  condition: boolean,
  delay = 0
) => {
  useEffect(() => {
    if (condition) {
      const timer = setTimeout(preloadFn, delay);
      return () => clearTimeout(timer);
    }
  }, [condition, delay, preloadFn]);
};

// Exemples d'utilisation pour mobile
export const LazyMobileSearchPage = createLazyComponent(
  () => import('../pages/SearchResultsPage'),
  'Chargement de la recherche...'
);

export const LazyMobileBookingPage = createLazyComponent(
  () => import('../pages/Booking'),
  'Chargement des réservations...'
);

export const LazyMobileProfilePage = createLazyComponent(
  () => import('../pages/AccountPage'),
  'Chargement du profil...'
);
