'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Types pour le Dashboard Trimurti Amélioré avec Audit
interface CosmicEnergy {
  brahma: number;
  vishnu: number;
  shiva: number;
}

interface CosmicPhase {
  dominant: 'brahma' | 'vishnu' | 'shiva' | 'equilibrium';
  intensity: number;
  duration: number;
  startTime: Date;
  nextTransition: Date;
}

interface CosmicAgent {
  id: string;
  name: string;
  cosmicAffinity: 'brahma' | 'vishnu' | 'shiva';
  energyLevel: number;
  cosmicMode: boolean;
  status: 'active' | 'dormant' | 'cosmic' | 'warning' | 'critical';
  // Nouvelles métriques d'audit
  responseTime: number;
  throughput: number;
  errorRate: number;
  lastHealthCheck: number;
  communicationLatency: number;
  coordinationEfficiency: number;
}

// Nouvelles interfaces pour l'audit
interface SystemMetrics {
  healthScore: number;
  totalCommunications: number;
  averageLatency: number;
  coordinationEfficiency: number;
  errorRate: number;
  activeConnections: number;
  bottlenecks: string[];
  recommendations: string[];
}

interface PredictiveAlert {
  id: string;
  type: 'performance_degradation' | 'coordination_failure' | 'resource_exhaustion' | 'network_partition';
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  predictedTime: number;
  description: string;
  affectedAgents: string[];
  timestamp: number;
}

interface PerformanceMetric {
  timestamp: number;
  healthScore: number;
  latency: number;
  efficiency: number;
  errorRate: number;
}

/**
 * 🕉️ Dashboard Trimurti - Interface de Contrôle Cosmique
 * Visualisation et contrôle des énergies cosmiques d'Hanuman
 */
export default function HanumanTrimurtiDashboard() {
  const [cosmicEnergy, setCosmicEnergy] = useState<CosmicEnergy>({
    brahma: 0.33,
    vishnu: 0.33,
    shiva: 0.33
  });

  const [currentPhase, setCurrentPhase] = useState<CosmicPhase>({
    dominant: 'equilibrium',
    intensity: 0.5,
    duration: 3600000,
    startTime: new Date(),
    nextTransition: new Date(Date.now() + 3600000)
  });

  const [cosmicAgents, setCosmicAgents] = useState<CosmicAgent[]>([
    // Agents Brahma (Créateurs)
    {
      id: 'cortex-creatif', name: 'Cortex Créatif', cosmicAffinity: 'brahma',
      energyLevel: 0.8, cosmicMode: true, status: 'cosmic',
      responseTime: 120, throughput: 85, errorRate: 0.02, lastHealthCheck: Date.now(),
      communicationLatency: 45, coordinationEfficiency: 0.92
    },
    {
      id: 'agent-frontend', name: 'Agent Frontend', cosmicAffinity: 'brahma',
      energyLevel: 0.7, cosmicMode: false, status: 'active',
      responseTime: 95, throughput: 78, errorRate: 0.01, lastHealthCheck: Date.now(),
      communicationLatency: 32, coordinationEfficiency: 0.88
    },
    {
      id: 'agent-web-research', name: 'Web Research', cosmicAffinity: 'brahma',
      energyLevel: 0.9, cosmicMode: true, status: 'cosmic',
      responseTime: 180, throughput: 92, errorRate: 0.03, lastHealthCheck: Date.now(),
      communicationLatency: 67, coordinationEfficiency: 0.95
    },

    // Agents Vishnu (Conservateurs)
    {
      id: 'agent-security', name: 'Agent Security', cosmicAffinity: 'vishnu',
      energyLevel: 0.95, cosmicMode: true, status: 'cosmic',
      responseTime: 85, throughput: 96, errorRate: 0.005, lastHealthCheck: Date.now(),
      communicationLatency: 28, coordinationEfficiency: 0.98
    },
    {
      id: 'agent-backend', name: 'Agent Backend', cosmicAffinity: 'vishnu',
      energyLevel: 0.85, cosmicMode: false, status: 'active',
      responseTime: 110, throughput: 88, errorRate: 0.015, lastHealthCheck: Date.now(),
      communicationLatency: 41, coordinationEfficiency: 0.91
    },
    {
      id: 'agent-documentation', name: 'Documentation', cosmicAffinity: 'vishnu',
      energyLevel: 0.6, cosmicMode: false, status: 'warning',
      responseTime: 250, throughput: 65, errorRate: 0.08, lastHealthCheck: Date.now(),
      communicationLatency: 89, coordinationEfficiency: 0.72
    },

    // Agents Shiva (Transformateurs)
    {
      id: 'agent-qa', name: 'Agent QA', cosmicAffinity: 'shiva',
      energyLevel: 0.75, cosmicMode: true, status: 'cosmic',
      responseTime: 140, throughput: 82, errorRate: 0.025, lastHealthCheck: Date.now(),
      communicationLatency: 52, coordinationEfficiency: 0.89
    },
    {
      id: 'agent-devops', name: 'Agent DevOps', cosmicAffinity: 'shiva',
      energyLevel: 0.8, cosmicMode: false, status: 'active',
      responseTime: 105, throughput: 90, errorRate: 0.012, lastHealthCheck: Date.now(),
      communicationLatency: 38, coordinationEfficiency: 0.93
    },
    {
      id: 'agent-performance', name: 'Performance', cosmicAffinity: 'shiva',
      energyLevel: 0.9, cosmicMode: true, status: 'cosmic',
      responseTime: 75, throughput: 98, errorRate: 0.008, lastHealthCheck: Date.now(),
      communicationLatency: 22, coordinationEfficiency: 0.97
    }
  ]);

  // Nouveaux états pour l'audit
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    healthScore: 0.92,
    totalCommunications: 15847,
    averageLatency: 118,
    coordinationEfficiency: 0.91,
    errorRate: 0.018,
    activeConnections: 24,
    bottlenecks: [],
    recommendations: []
  });

  const [predictiveAlerts, setPredictiveAlerts] = useState<PredictiveAlert[]>([]);
  const [performanceHistory, setPerformanceHistory] = useState<PerformanceMetric[]>([]);
  const [showAdvancedMetrics, setShowAdvancedMetrics] = useState(false);

  const [cosmicTime, setCosmicTime] = useState(new Date());
  const [meditationMode, setMeditationMode] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const metricsCanvasRef = useRef<HTMLCanvasElement>(null);

  // Simulation temps cosmique et monitoring avancé
  useEffect(() => {
    const timer = setInterval(() => {
      setCosmicTime(new Date());
      updateCosmicPhase();
      simulateCosmicFluctuations();
      updateSystemMetrics();
      generatePredictiveAlerts();
      updatePerformanceHistory();
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Nouvelles fonctions de monitoring avancé
  const updateSystemMetrics = () => {
    const agents = cosmicAgents;
    const avgResponseTime = agents.reduce((sum, agent) => sum + agent.responseTime, 0) / agents.length;
    const avgThroughput = agents.reduce((sum, agent) => sum + agent.throughput, 0) / agents.length;
    const avgErrorRate = agents.reduce((sum, agent) => sum + agent.errorRate, 0) / agents.length;
    const avgCoordinationEfficiency = agents.reduce((sum, agent) => sum + agent.coordinationEfficiency, 0) / agents.length;

    // Calcul du score de santé global
    const healthScore = Math.max(0, Math.min(1,
      (avgThroughput / 100) * 0.3 +
      (1 - avgErrorRate) * 0.3 +
      avgCoordinationEfficiency * 0.4
    ));

    // Détection des goulots d'étranglement
    const bottlenecks: string[] = [];
    if (avgResponseTime > 200) bottlenecks.push('high-latency');
    if (avgErrorRate > 0.05) bottlenecks.push('high-error-rate');
    if (avgCoordinationEfficiency < 0.8) bottlenecks.push('poor-coordination');

    // Génération de recommandations
    const recommendations: string[] = [];
    if (bottlenecks.includes('high-latency')) {
      recommendations.push('Optimiser les communications inter-agent');
    }
    if (bottlenecks.includes('high-error-rate')) {
      recommendations.push('Améliorer la gestion des erreurs');
    }
    if (bottlenecks.includes('poor-coordination')) {
      recommendations.push('Optimiser les algorithmes de consensus');
    }

    setSystemMetrics(prev => ({
      ...prev,
      healthScore,
      averageLatency: avgResponseTime,
      coordinationEfficiency: avgCoordinationEfficiency,
      errorRate: avgErrorRate,
      totalCommunications: prev.totalCommunications + Math.floor(Math.random() * 10),
      bottlenecks,
      recommendations
    }));
  };

  const generatePredictiveAlerts = () => {
    const now = Date.now();
    const newAlerts: PredictiveAlert[] = [];

    // Simulation d'alertes prédictives basées sur les métriques
    cosmicAgents.forEach(agent => {
      // Alerte de dégradation de performance
      if (agent.responseTime > 200 && agent.errorRate > 0.05) {
        const alertExists = predictiveAlerts.some(alert =>
          alert.type === 'performance_degradation' &&
          alert.affectedAgents.includes(agent.id)
        );

        if (!alertExists && Math.random() > 0.95) { // 5% de chance
          newAlerts.push({
            id: `alert_${now}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'performance_degradation',
            severity: agent.responseTime > 300 ? 'critical' : 'high',
            confidence: 0.85,
            predictedTime: now + (5 * 60 * 1000), // Dans 5 minutes
            description: `Dégradation de performance prédite pour ${agent.name}`,
            affectedAgents: [agent.id],
            timestamp: now
          });
        }
      }

      // Alerte d'échec de coordination
      if (agent.coordinationEfficiency < 0.7 && Math.random() > 0.98) {
        newAlerts.push({
          id: `alert_${now}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'coordination_failure',
          severity: 'medium',
          confidence: 0.75,
          predictedTime: now + (10 * 60 * 1000), // Dans 10 minutes
          description: `Risque d'échec de coordination pour ${agent.name}`,
          affectedAgents: [agent.id],
          timestamp: now
        });
      }
    });

    if (newAlerts.length > 0) {
      setPredictiveAlerts(prev => [...newAlerts, ...prev.slice(0, 9)]); // Garder 10 alertes max
    }
  };

  const updatePerformanceHistory = () => {
    const newMetric: PerformanceMetric = {
      timestamp: Date.now(),
      healthScore: systemMetrics.healthScore,
      latency: systemMetrics.averageLatency,
      efficiency: systemMetrics.coordinationEfficiency,
      errorRate: systemMetrics.errorRate
    };

    setPerformanceHistory(prev => [...prev.slice(-59), newMetric]); // Garder 60 points
  };

  // Animation du mandala cosmique et graphiques de performance
  useEffect(() => {
    if (canvasRef.current) {
      drawCosmicMandala();
    }
    if (metricsCanvasRef.current && performanceHistory.length > 1) {
      drawPerformanceMetrics();
    }
  }, [cosmicEnergy, currentPhase, performanceHistory]);

  const updateCosmicPhase = () => {
    const hour = new Date().getHours();
    let newDominant: CosmicPhase['dominant'];

    if (hour >= 6 && hour < 12) {
      newDominant = 'brahma';
    } else if (hour >= 12 && hour < 18) {
      newDominant = 'vishnu';
    } else if (hour >= 18 && hour < 24) {
      newDominant = 'shiva';
    } else {
      newDominant = 'equilibrium';
    }

    if (newDominant !== currentPhase.dominant) {
      setCurrentPhase(prev => ({
        ...prev,
        dominant: newDominant,
        startTime: new Date(),
        nextTransition: new Date(Date.now() + 3600000)
      }));

      updateCosmicEnergies(newDominant);
    }
  };

  const updateCosmicEnergies = (dominant: CosmicPhase['dominant']) => {
    const intensity = 0.7;
    const baseEnergy = (1 - intensity) / 3;
    const dominantEnergy = baseEnergy + intensity;

    switch (dominant) {
      case 'brahma':
        setCosmicEnergy({ brahma: dominantEnergy, vishnu: baseEnergy, shiva: baseEnergy });
        break;
      case 'vishnu':
        setCosmicEnergy({ brahma: baseEnergy, vishnu: dominantEnergy, shiva: baseEnergy });
        break;
      case 'shiva':
        setCosmicEnergy({ brahma: baseEnergy, vishnu: baseEnergy, shiva: dominantEnergy });
        break;
      default:
        setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });
    }
  };

  const simulateCosmicFluctuations = () => {
    setCosmicAgents(prev => prev.map(agent => {
      // Simulation des fluctuations cosmiques avec nouvelles métriques
      const energyFluctuation = (Math.random() - 0.5) * 0.05;
      const newEnergyLevel = Math.max(0.1, Math.min(1, agent.energyLevel + energyFluctuation));

      // Simulation des métriques de performance
      const responseTimeFluctuation = (Math.random() - 0.5) * 20;
      const throughputFluctuation = (Math.random() - 0.5) * 10;
      const errorRateFluctuation = (Math.random() - 0.5) * 0.01;
      const latencyFluctuation = (Math.random() - 0.5) * 10;
      const efficiencyFluctuation = (Math.random() - 0.5) * 0.05;

      // Détermination du statut basé sur les métriques
      let newStatus = agent.status;
      if (agent.responseTime > 300 || agent.errorRate > 0.1) {
        newStatus = 'critical';
      } else if (agent.responseTime > 200 || agent.errorRate > 0.05) {
        newStatus = 'warning';
      } else if (agent.cosmicMode) {
        newStatus = 'cosmic';
      } else {
        newStatus = 'active';
      }

      return {
        ...agent,
        energyLevel: newEnergyLevel,
        responseTime: Math.max(50, agent.responseTime + responseTimeFluctuation),
        throughput: Math.max(10, Math.min(100, agent.throughput + throughputFluctuation)),
        errorRate: Math.max(0, Math.min(0.2, agent.errorRate + errorRateFluctuation)),
        communicationLatency: Math.max(10, agent.communicationLatency + latencyFluctuation),
        coordinationEfficiency: Math.max(0.1, Math.min(1, agent.coordinationEfficiency + efficiencyFluctuation)),
        status: newStatus,
        lastHealthCheck: Date.now()
      };
    }));
  };

  const drawCosmicMandala = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // Effacer le canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Dessiner le mandala Trimurti
    const angles = {
      brahma: 0,
      vishnu: (2 * Math.PI) / 3,
      shiva: (4 * Math.PI) / 3
    };

    // Cercle central
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI);
    ctx.fillStyle = `rgba(255, 255, 255, 0.1)`;
    ctx.fill();

    // Secteurs cosmiques
    Object.entries(cosmicEnergy).forEach(([principle, energy], index) => {
      const angle = angles[principle as keyof typeof angles];
      const colors = {
        brahma: `rgba(255, 215, 0, ${energy})`, // Or
        vishnu: `rgba(65, 105, 225, ${energy})`, // Bleu royal
        shiva: `rgba(255, 69, 0, ${energy})` // Rouge-orange
      };

      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, angle - Math.PI/3, angle + Math.PI/3);
      ctx.closePath();
      ctx.fillStyle = colors[principle as keyof typeof colors];
      ctx.fill();

      // Symboles
      const symbolX = centerX + Math.cos(angle) * radius * 0.7;
      const symbolY = centerY + Math.sin(angle) * radius * 0.7;

      ctx.fillStyle = 'white';
      ctx.font = '24px serif';
      ctx.textAlign = 'center';

      const symbols = { brahma: '🌅', vishnu: '🌊', shiva: '🔥' };
      ctx.fillText(symbols[principle as keyof typeof symbols], symbolX, symbolY);
    });

    // Symbole OM central
    ctx.fillStyle = 'white';
    ctx.font = '32px serif';
    ctx.textAlign = 'center';
    ctx.fillText('🕉️', centerX, centerY + 10);
  };

  // Nouvelle fonction pour dessiner les graphiques de performance
  const drawPerformanceMetrics = () => {
    const canvas = metricsCanvasRef.current;
    if (!canvas || performanceHistory.length < 2) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    const graphWidth = width - 2 * padding;
    const graphHeight = height - 2 * padding;

    // Effacer le canvas
    ctx.clearRect(0, 0, width, height);

    // Dessiner les axes
    ctx.strokeStyle = '#4B5563';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();

    // Dessiner les métriques
    const metrics = ['healthScore', 'efficiency', 'errorRate'];
    const colors = ['#10B981', '#3B82F6', '#EF4444'];

    metrics.forEach((metric, index) => {
      ctx.strokeStyle = colors[index];
      ctx.lineWidth = 2;
      ctx.beginPath();

      performanceHistory.forEach((point, i) => {
        const x = padding + (i / (performanceHistory.length - 1)) * graphWidth;
        let value = point[metric as keyof PerformanceMetric] as number;

        // Normalisation des valeurs
        if (metric === 'errorRate') value = 1 - value; // Inverser pour que plus haut = mieux

        const y = height - padding - (value * graphHeight);

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();
    });

    // Légende
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    metrics.forEach((metric, index) => {
      ctx.fillStyle = colors[index];
      const labels = {
        healthScore: 'Score de Santé',
        efficiency: 'Efficacité',
        errorRate: 'Fiabilité'
      };
      ctx.fillText(labels[metric as keyof typeof labels], 10, 20 + index * 15);
    });
  };

  // Fonction pour obtenir la couleur du statut d'agent
  const getAgentStatusColor = (status: string) => {
    const colors = {
      cosmic: 'border-yellow-400 bg-yellow-400/10',
      active: 'border-green-500 bg-green-500/10',
      warning: 'border-orange-500 bg-orange-500/10',
      critical: 'border-red-500 bg-red-500/10',
      dormant: 'border-gray-500 bg-gray-500/10'
    };
    return colors[status as keyof typeof colors] || colors.active;
  };

  // Fonction pour obtenir l'icône du statut
  const getAgentStatusIcon = (status: string) => {
    const icons = {
      cosmic: '✨',
      active: '🟢',
      warning: '⚠️',
      critical: '🔴',
      dormant: '⚪'
    };
    return icons[status as keyof typeof icons] || '🟢';
  };

  // Fonction pour obtenir la couleur de sévérité des alertes
  const getAlertSeverityColor = (severity: string) => {
    const colors = {
      low: 'bg-blue-100 border-blue-300 text-blue-800',
      medium: 'bg-yellow-100 border-yellow-300 text-yellow-800',
      high: 'bg-orange-100 border-orange-300 text-orange-800',
      critical: 'bg-red-100 border-red-300 text-red-800'
    };
    return colors[severity as keyof typeof colors] || colors.low;
  };

  const invokeCosmicEnergy = (principle: 'brahma' | 'vishnu' | 'shiva') => {
    console.log(`🕉️ Invocation de l'énergie ${principle.toUpperCase()}`);

    setCurrentPhase(prev => ({
      ...prev,
      dominant: principle,
      intensity: 0.9,
      startTime: new Date()
    }));

    updateCosmicEnergies(principle);

    // Activer les agents correspondants
    setCosmicAgents(prev => prev.map(agent =>
      agent.cosmicAffinity === principle
        ? { ...agent, cosmicMode: true, status: 'cosmic', energyLevel: Math.min(1, agent.energyLevel + 0.2) }
        : agent
    ));
  };

  const startCosmicMeditation = async () => {
    setMeditationMode(true);
    console.log('🧘 Début méditation cosmique...');

    // Simulation méditation de 108 cycles
    for (let i = 0; i < 108; i++) {
      await new Promise(resolve => setTimeout(resolve, 50));

      if (i % 27 === 0) {
        console.log(`🕉️ OM - Cycle ${i}/108`);
      }
    }

    // Rééquilibrage final
    setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });
    setCurrentPhase(prev => ({ ...prev, dominant: 'equilibrium', intensity: 0.5 }));

    setMeditationMode(false);
    console.log('✨ Méditation cosmique complétée - Harmonie restaurée');
  };

  const getPhaseColor = () => {
    const colors = {
      brahma: 'from-yellow-400 to-orange-500',
      vishnu: 'from-blue-500 to-indigo-600',
      shiva: 'from-red-500 to-orange-600',
      equilibrium: 'from-gray-400 to-gray-600'
    };
    return colors[currentPhase.dominant];
  };

  const getPhaseEmoji = () => {
    const emojis = {
      brahma: '🌅',
      vishnu: '🌊',
      shiva: '🔥',
      equilibrium: '⚖️'
    };
    return emojis[currentPhase.dominant];
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      {/* En-tête cosmique */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold sacred-text mb-4">
          🕉️ DASHBOARD TRIMURTI AVANCÉ
        </h1>
        <p className="text-xl text-gray-300">
          Contrôle des Énergies Cosmiques d'Hanuman avec Monitoring IA
        </p>
        <div className="mt-4 text-sm text-gray-400">
          {cosmicTime.toLocaleString('fr-FR')} • Fréquence: 432Hz • AUM HANUMATE NAMAHA
        </div>

        {/* Bouton pour basculer les métriques avancées */}
        <button
          onClick={() => setShowAdvancedMetrics(!showAdvancedMetrics)}
          className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors"
        >
          {showAdvancedMetrics ? '🔽 Masquer Métriques Avancées' : '🔼 Afficher Métriques Avancées'}
        </button>
      </div>

      {/* Métriques système globales */}
      <div className="mb-8 bg-gray-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4 text-center">📊 Métriques Système Globales</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h3 className="text-sm font-medium text-green-600">Score de Santé</h3>
            <p className="text-2xl font-bold text-green-800">
              {(systemMetrics.healthScore * 100).toFixed(1)}%
            </p>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-600">Communications</h3>
            <p className="text-2xl font-bold text-blue-800">
              {systemMetrics.totalCommunications.toLocaleString()}
            </p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <h3 className="text-sm font-medium text-purple-600">Latence Moy.</h3>
            <p className="text-2xl font-bold text-purple-800">
              {systemMetrics.averageLatency.toFixed(0)}ms
            </p>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
            <h3 className="text-sm font-medium text-orange-600">Efficacité Coord.</h3>
            <p className="text-2xl font-bold text-orange-800">
              {(systemMetrics.coordinationEfficiency * 100).toFixed(1)}%
            </p>
          </div>

          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <h3 className="text-sm font-medium text-red-600">Taux d'Erreur</h3>
            <p className="text-2xl font-bold text-red-800">
              {(systemMetrics.errorRate * 100).toFixed(2)}%
            </p>
          </div>

          <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
            <h3 className="text-sm font-medium text-indigo-600">Connexions</h3>
            <p className="text-2xl font-bold text-indigo-800">
              {systemMetrics.activeConnections}
            </p>
          </div>
        </div>

        {/* Alertes et recommandations */}
        {(systemMetrics.bottlenecks.length > 0 || systemMetrics.recommendations.length > 0) && (
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-4">
            {systemMetrics.bottlenecks.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-red-800 mb-2">⚠️ Goulots d'étranglement</h3>
                <ul className="space-y-1">
                  {systemMetrics.bottlenecks.map((bottleneck, index) => (
                    <li key={index} className="text-red-700 text-sm">• {bottleneck}</li>
                  ))}
                </ul>
              </div>
            )}

            {systemMetrics.recommendations.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">💡 Recommandations</h3>
                <ul className="space-y-1">
                  {systemMetrics.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-blue-700 text-sm">• {recommendation}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Phase cosmique actuelle */}
      <div className="text-center mb-8">
        <div className={`inline-block px-6 py-3 rounded-full bg-gradient-to-r ${getPhaseColor()} text-white font-bold text-lg`}>
          {getPhaseEmoji()} Phase {currentPhase.dominant.toUpperCase()}
          <span className="ml-2 text-sm opacity-80">
            (Intensité: {Math.round(currentPhase.intensity * 100)}%)
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Mandala Trimurti */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4 text-center">🌟 Mandala Cosmique</h2>
          <div className="flex justify-center">
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              className="border border-gray-600 rounded-full"
            />
          </div>

          {/* Métriques énergétiques */}
          <div className="mt-6 space-y-3">
            {Object.entries(cosmicEnergy).map(([principle, energy]) => (
              <div key={principle} className="flex items-center justify-between">
                <span className="capitalize font-medium">
                  {principle === 'brahma' && '🌅'}
                  {principle === 'vishnu' && '🌊'}
                  {principle === 'shiva' && '🔥'}
                  {principle}
                </span>
                <div className="flex-1 mx-4">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        principle === 'brahma' ? 'bg-yellow-400' :
                        principle === 'vishnu' ? 'bg-blue-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${energy * 100}%` }}
                    />
                  </div>
                </div>
                <span className="text-sm font-mono">
                  {Math.round(energy * 100)}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Agents cosmiques */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4">🤖 Agents Cosmiques</h2>
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {cosmicAgents.map(agent => (
              <motion.div
                key={agent.id}
                className={`p-3 rounded-lg border ${getAgentStatusColor(agent.status)}`}
                animate={{
                  scale: agent.cosmicMode ? 1.02 : 1,
                  boxShadow: agent.cosmicMode ? '0 0 20px rgba(255, 215, 0, 0.3)' : 'none'
                }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">
                      {agent.cosmicAffinity === 'brahma' && '🌅'}
                      {agent.cosmicAffinity === 'vishnu' && '🌊'}
                      {agent.cosmicAffinity === 'shiva' && '🔥'}
                    </span>
                    <div>
                      <div className="font-medium flex items-center gap-2">
                        {agent.name}
                        <span className="text-sm">{getAgentStatusIcon(agent.status)}</span>
                      </div>
                      <div className="text-xs text-gray-400 capitalize">
                        {agent.cosmicAffinity} • {agent.status}
                      </div>
                      {showAdvancedMetrics && (
                        <div className="text-xs text-gray-500 mt-1">
                          RT: {agent.responseTime}ms | TP: {agent.throughput} | Err: {(agent.errorRate * 100).toFixed(1)}%
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-mono">
                      {Math.round(agent.energyLevel * 100)}%
                    </div>
                    <div className={`text-xs ${
                      agent.cosmicMode ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      {agent.cosmicMode ? '✨ Cosmique' : getAgentStatusIcon(agent.status) + ' ' + agent.status}
                    </div>
                    {showAdvancedMetrics && (
                      <div className="text-xs text-gray-500 mt-1">
                        Coord: {(agent.coordinationEfficiency * 100).toFixed(0)}%
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Sections avancées de monitoring */}
      {showAdvancedMetrics && (
        <div className="mt-8 space-y-8">
          {/* Alertes prédictives */}
          {predictiveAlerts.length > 0 && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4">🚨 Alertes Prédictives</h2>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {predictiveAlerts.map(alert => (
                  <motion.div
                    key={alert.id}
                    className={`p-4 rounded-lg border ${getAlertSeverityColor(alert.severity)}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-semibold flex items-center gap-2">
                          {alert.severity === 'critical' && '🔴'}
                          {alert.severity === 'high' && '🟠'}
                          {alert.severity === 'medium' && '🟡'}
                          {alert.severity === 'low' && '🔵'}
                          {alert.description}
                        </div>
                        <div className="text-sm mt-1 opacity-80">
                          Type: {alert.type} | Confiance: {(alert.confidence * 100).toFixed(0)}%
                        </div>
                        <div className="text-xs mt-1 opacity-60">
                          Agents affectés: {alert.affectedAgents.join(', ')}
                        </div>
                      </div>
                      <div className="text-right text-sm">
                        <div className="font-mono">
                          {new Date(alert.predictedTime).toLocaleTimeString()}
                        </div>
                        <div className="text-xs opacity-60">
                          {new Date(alert.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Graphiques de performance */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">📈 Tendances de Performance</h2>
            <div className="flex justify-center">
              <canvas
                ref={metricsCanvasRef}
                width={800}
                height={300}
                className="border border-gray-600 rounded bg-gray-900"
              />
            </div>
            <div className="mt-4 text-center text-sm text-gray-400">
              Évolution des métriques système sur les 60 dernières secondes
            </div>
          </div>

          {/* Métriques détaillées par agent */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">🔍 Métriques Détaillées par Agent</h2>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-600">
                    <th className="text-left p-2">Agent</th>
                    <th className="text-left p-2">Statut</th>
                    <th className="text-left p-2">Temps Réponse</th>
                    <th className="text-left p-2">Débit</th>
                    <th className="text-left p-2">Taux Erreur</th>
                    <th className="text-left p-2">Latence Comm.</th>
                    <th className="text-left p-2">Efficacité Coord.</th>
                    <th className="text-left p-2">Dernière Vérif.</th>
                  </tr>
                </thead>
                <tbody>
                  {cosmicAgents.map(agent => (
                    <tr key={agent.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                      <td className="p-2">
                        <div className="flex items-center gap-2">
                          {agent.cosmicAffinity === 'brahma' && '🌅'}
                          {agent.cosmicAffinity === 'vishnu' && '🌊'}
                          {agent.cosmicAffinity === 'shiva' && '🔥'}
                          {agent.name}
                        </div>
                      </td>
                      <td className="p-2">
                        <span className="flex items-center gap-1">
                          {getAgentStatusIcon(agent.status)}
                          {agent.status}
                        </span>
                      </td>
                      <td className="p-2 font-mono">{agent.responseTime}ms</td>
                      <td className="p-2 font-mono">{agent.throughput}</td>
                      <td className="p-2 font-mono">{(agent.errorRate * 100).toFixed(2)}%</td>
                      <td className="p-2 font-mono">{agent.communicationLatency}ms</td>
                      <td className="p-2 font-mono">{(agent.coordinationEfficiency * 100).toFixed(1)}%</td>
                      <td className="p-2 text-xs">
                        {new Date(agent.lastHealthCheck).toLocaleTimeString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Contrôles cosmiques */}
      <div className="mt-8 bg-gray-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4 text-center">⚡ Contrôles Cosmiques</h2>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Invocations énergétiques */}
          <button
            onClick={() => invokeCosmicEnergy('brahma')}
            className="p-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🌅 Invoquer BRAHMA
            <div className="text-xs mt-1 opacity-80">Énergie Créatrice</div>
          </button>

          <button
            onClick={() => invokeCosmicEnergy('vishnu')}
            className="p-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🌊 Invoquer VISHNU
            <div className="text-xs mt-1 opacity-80">Énergie Conservatrice</div>
          </button>

          <button
            onClick={() => invokeCosmicEnergy('shiva')}
            className="p-4 bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🔥 Invoquer SHIVA
            <div className="text-xs mt-1 opacity-80">Énergie Transformatrice</div>
          </button>

          <button
            onClick={startCosmicMeditation}
            disabled={meditationMode}
            className={`p-4 rounded-lg font-bold transition-all ${
              meditationMode
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:scale-105'
            }`}
          >
            {meditationMode ? (
              <>
                🧘 Méditation...
                <div className="text-xs mt-1 opacity-80">108 cycles OM</div>
              </>
            ) : (
              <>
                🕉️ Méditation Cosmique
                <div className="text-xs mt-1 opacity-80">Rééquilibrage</div>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Mantras cosmiques */}
      <div className="mt-8 text-center">
        <div className="bg-gray-800 rounded-lg p-4 inline-block">
          <div className="text-lg font-bold sacred-text mb-2">
            Mantras Cosmiques Actifs
          </div>
          <div className="space-y-1 text-sm text-gray-300">
            <div>🌅 AUM BRAHMAYE NAMAHA - Création</div>
            <div>🌊 AUM VISHNAVE NAMAHA - Conservation</div>
            <div>🔥 AUM SHIVAYA NAMAHA - Transformation</div>
            <div className="text-yellow-400 font-bold">🐒 AUM HANUMATE NAMAHA - Unité Divine</div>
          </div>
        </div>
      </div>
    </div>
  );
}
