#!/usr/bin/env node

/**
 * 🧪 SCRIPT DE TEST DU DASHBOARD TRIMURTI AVANCÉ
 * 
 * Test des nouvelles fonctionnalités d'audit et de monitoring
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🕉️ DÉMARRAGE DU DASHBOARD TRIMURTI AVANCÉ');
console.log('==========================================');

// Configuration
const config = {
  port: 3000,
  env: 'development',
  features: {
    advancedMetrics: true,
    predictiveAlerts: true,
    performanceGraphs: true,
    intelligentCache: true,
    loadBalancer: true
  }
};

console.log('📊 Configuration du test:');
console.log(`   Port: ${config.port}`);
console.log(`   Environnement: ${config.env}`);
console.log(`   Fonctionnalités activées:`);
Object.entries(config.features).forEach(([feature, enabled]) => {
  console.log(`     ${enabled ? '✅' : '❌'} ${feature}`);
});

console.log('\n🚀 Démarrage du serveur Next.js...');

// Démarrage du serveur de développement
const nextProcess = spawn('npm', ['run', 'dev'], {
  cwd: __dirname,
  stdio: 'inherit',
  env: {
    ...process.env,
    PORT: config.port,
    NODE_ENV: config.env,
    NEXT_PUBLIC_ADVANCED_METRICS: config.features.advancedMetrics,
    NEXT_PUBLIC_PREDICTIVE_ALERTS: config.features.predictiveAlerts,
    NEXT_PUBLIC_PERFORMANCE_GRAPHS: config.features.performanceGraphs
  }
});

// Gestion des événements
nextProcess.on('spawn', () => {
  console.log('✅ Serveur Next.js démarré avec succès');
  console.log(`🌐 Dashboard disponible sur: http://localhost:${config.port}`);
  console.log('\n📋 Pages de test disponibles:');
  console.log(`   🏠 Accueil: http://localhost:${config.port}`);
  console.log(`   🕉️ Dashboard Trimurti: http://localhost:${config.port}/trimurti-dashboard`);
  console.log(`   💬 Interface Chat: http://localhost:${config.port}/chat`);
  console.log(`   🎛️ Contrôle IDE: http://localhost:${config.port}/ide-control`);
  
  console.log('\n🧪 Tests recommandés:');
  console.log('   1. Vérifier les métriques système globales');
  console.log('   2. Activer le mode métriques avancées');
  console.log('   3. Observer les alertes prédictives');
  console.log('   4. Analyser les graphiques de performance');
  console.log('   5. Tester les invocations cosmiques');
  console.log('   6. Vérifier la méditation cosmique');
  
  console.log('\n⚡ Fonctionnalités d\'audit à tester:');
  console.log('   📊 Métriques inter-agent en temps réel');
  console.log('   🚨 Système d\'alertes prédictives');
  console.log('   📈 Graphiques de performance');
  console.log('   🔍 Tableau détaillé des agents');
  console.log('   🎯 Score de santé système');
  console.log('   💡 Recommandations automatiques');
  
  console.log('\n🔧 Commandes de test:');
  console.log('   Ctrl+C : Arrêter le serveur');
  console.log('   npm run build : Construire pour production');
  console.log('   npm run start : Démarrer en mode production');
});

nextProcess.on('error', (error) => {
  console.error('❌ Erreur lors du démarrage du serveur:', error);
  process.exit(1);
});

nextProcess.on('exit', (code, signal) => {
  if (code !== null) {
    console.log(`\n🛑 Serveur arrêté avec le code: ${code}`);
  } else if (signal !== null) {
    console.log(`\n🛑 Serveur arrêté par le signal: ${signal}`);
  }
  
  console.log('\n🕉️ AUM HANUMATE NAMAHA - Test terminé');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du test en cours...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt forcé du test...');
  nextProcess.kill('SIGTERM');
});

// Simulation de données de test après démarrage
setTimeout(() => {
  console.log('\n🎭 Simulation des données de test...');
  
  // Simulation d'alertes prédictives
  console.log('🚨 Génération d\'alertes prédictives de test...');
  
  // Simulation de métriques de performance
  console.log('📊 Génération de métriques de performance...');
  
  // Simulation de fluctuations cosmiques
  console.log('🌊 Simulation des fluctuations cosmiques...');
  
  console.log('✅ Données de test initialisées');
  console.log('\n🎯 Le dashboard est maintenant prêt pour les tests d\'audit!');
  
}, 5000); // Attendre 5 secondes après le démarrage

// Affichage des informations de debug
setTimeout(() => {
  console.log('\n🔍 Informations de debug:');
  console.log(`   PID du processus: ${nextProcess.pid}`);
  console.log(`   Répertoire de travail: ${__dirname}`);
  console.log(`   Variables d'environnement actives:`);
  console.log(`     NODE_ENV: ${process.env.NODE_ENV || 'non défini'}`);
  console.log(`     PORT: ${process.env.PORT || 'non défini'}`);
  
  console.log('\n📝 Logs à surveiller:');
  console.log('   - Métriques de communication inter-agent');
  console.log('   - Alertes prédictives générées');
  console.log('   - Fluctuations des énergies cosmiques');
  console.log('   - Mises à jour des graphiques de performance');
  
}, 10000); // Afficher après 10 secondes
