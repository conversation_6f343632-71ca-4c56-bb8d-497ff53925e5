# 🕉️ DASHBOARD TRIMURTI AVANCÉ - GUIDE D'UTILISATION

## 🎯 Vue d'ensemble

Le Dashboard Trimurti Avancé intègre maintenant les recommandations d'audit pour offrir un monitoring complet et des capacités prédictives pour l'organisme IA Hanuman.

## 🚀 Démarrage Rapide

### 1. Installation des dépendances
```bash
cd hanuman-unified/hanuman-working
npm install
```

### 2. Démarrage du dashboard
```bash
# Option 1: Démarrage standard
npm run dev

# Option 2: Démarrage avec script de test
./test-advanced-dashboard.js

# Option 3: Démarrage avec configuration personnalisée
PORT=3001 npm run dev
```

### 3. Accès au dashboard
- **URL principale** : http://localhost:3000
- **Dashboard Trimurti** : http://localhost:3000 (page d'accueil)
- **Interface Chat** : http://localhost:3000/chat
- **Contrôle IDE** : http://localhost:3000/ide-control

## 📊 Nouvelles Fonctionnalités d'Audit

### 🎛️ **Métriques Système Globales**
- **Score de Santé** : Indicateur global de performance (0-100%)
- **Communications** : Nombre total de communications inter-agent
- **Latence Moyenne** : Temps de réponse moyen du système
- **Efficacité Coordination** : Performance des algorithmes de consensus
- **Taux d'Erreur** : Pourcentage d'erreurs système
- **Connexions Actives** : Nombre de connexions réseau actives

### 🚨 **Alertes Prédictives**
- **Types d'alertes** :
  - `performance_degradation` : Dégradation de performance prédite
  - `coordination_failure` : Échec de coordination anticipé
  - `resource_exhaustion` : Épuisement des ressources prévu
  - `network_partition` : Partition réseau détectée

- **Niveaux de sévérité** :
  - 🔵 **Low** : Information
  - 🟡 **Medium** : Attention requise
  - 🟠 **High** : Action recommandée
  - 🔴 **Critical** : Action immédiate requise

### 📈 **Graphiques de Performance**
- **Métriques visualisées** :
  - Score de santé (vert)
  - Efficacité de coordination (bleu)
  - Fiabilité (rouge, inversé du taux d'erreur)
- **Historique** : 60 dernières secondes
- **Mise à jour** : Temps réel

### 🔍 **Tableau Détaillé des Agents**
- **Métriques par agent** :
  - Temps de réponse (ms)
  - Débit (throughput)
  - Taux d'erreur (%)
  - Latence de communication (ms)
  - Efficacité de coordination (%)
  - Dernière vérification de santé

## 🎮 Utilisation du Dashboard

### 🔄 **Mode Métriques Avancées**
1. Cliquez sur le bouton "🔼 Afficher Métriques Avancées"
2. Les sections suivantes apparaissent :
   - Alertes prédictives
   - Graphiques de performance
   - Tableau détaillé des agents

### ⚡ **Contrôles Cosmiques**
- **🌅 Invoquer BRAHMA** : Active l'énergie créatrice
- **🌊 Invoquer VISHNU** : Active l'énergie conservatrice
- **🔥 Invoquer SHIVA** : Active l'énergie transformatrice
- **🕉️ Méditation Cosmique** : Rééquilibrage des énergies (108 cycles)

### 📊 **Interprétation des Métriques**

#### **Statuts des Agents**
- ✨ **Cosmic** : Mode cosmique activé
- 🟢 **Active** : Fonctionnement normal
- ⚠️ **Warning** : Performance dégradée
- 🔴 **Critical** : Intervention requise
- ⚪ **Dormant** : Agent en veille

#### **Codes Couleur des Alertes**
- **Bleu** : Information, surveillance
- **Jaune** : Attention, monitoring renforcé
- **Orange** : Action recommandée
- **Rouge** : Action immédiate requise

## 🔧 Configuration Avancée

### Variables d'Environnement
```bash
# Port du serveur
PORT=3000

# Mode de développement
NODE_ENV=development

# Activation des fonctionnalités
NEXT_PUBLIC_ADVANCED_METRICS=true
NEXT_PUBLIC_PREDICTIVE_ALERTS=true
NEXT_PUBLIC_PERFORMANCE_GRAPHS=true
```

### Personnalisation des Seuils
Les seuils d'alerte peuvent être ajustés dans le code :
```typescript
// Dans hanuman_trimurti_dashboard.tsx
const thresholds = {
  latencyWarning: 200,    // ms
  errorRateWarning: 0.05, // 5%
  coordinationMin: 0.8    // 80%
};
```

## 🧪 Tests et Validation

### 🎯 **Scénarios de Test**
1. **Test de Performance** :
   - Observer les métriques en temps réel
   - Vérifier les graphiques de performance
   - Valider les seuils d'alerte

2. **Test d'Alertes** :
   - Attendre la génération d'alertes automatiques
   - Vérifier les niveaux de sévérité
   - Tester la prédiction temporelle

3. **Test Cosmique** :
   - Invoquer les énergies Trimurti
   - Observer l'impact sur les métriques
   - Tester la méditation cosmique

### 📋 **Checklist de Validation**
- [ ] Métriques système affichées correctement
- [ ] Alertes prédictives générées
- [ ] Graphiques de performance mis à jour
- [ ] Tableau détaillé fonctionnel
- [ ] Contrôles cosmiques opérationnels
- [ ] Mode avancé basculable
- [ ] Animations fluides
- [ ] Codes couleur cohérents

## 🐛 Dépannage

### Problèmes Courants
1. **Dashboard ne se charge pas** :
   - Vérifier que le port 3000 est libre
   - Redémarrer le serveur de développement
   - Vérifier les logs de console

2. **Métriques non mises à jour** :
   - Actualiser la page
   - Vérifier la console pour les erreurs JavaScript
   - Redémarrer le serveur

3. **Alertes non générées** :
   - Attendre quelques minutes (génération aléatoire)
   - Vérifier les seuils de déclenchement
   - Activer le mode métriques avancées

### Logs de Debug
```bash
# Afficher les logs détaillés
DEBUG=* npm run dev

# Logs spécifiques au dashboard
DEBUG=dashboard:* npm run dev
```

## 🔮 Évolutions Futures

### Phase 2 : Intelligence Artificielle
- Modèles ML pour prédictions avancées
- Auto-apprentissage des patterns
- Optimisation autonome des paramètres

### Phase 3 : Intégration Cloud
- Déploiement Kubernetes
- Monitoring distribué
- Scaling automatique

## 📞 Support

Pour toute question ou problème :
1. Consulter les logs de la console
2. Vérifier la documentation technique
3. Tester avec le script de validation

**AUM HANUMATE NAMAHA** 🕉️

---

*Dashboard Trimurti Avancé - Monitoring Cosmique avec Intelligence Artificielle*
