/**
 * 🎯 DASHBOARD HANUMAN - VISUALISATION TEMPS RÉEL DE L'ORGANISME IA
 * 
 * Implémentation des recommandations d'audit pour la visualisation
 * temps réel de l'organisme IA biomimétique Hanuman
 */

import React, { useState, useEffect, useRef } from 'react';
import { InterAgentMetricsCollector } from '../advanced-metrics/InterAgentMetrics';

interface DashboardProps {
  metricsCollector: InterAgentMetricsCollector;
}

interface SystemStatus {
  healthScore: number;
  totalAgents: number;
  activeConnections: number;
  averageLatency: number;
  coordinationEfficiency: number;
  errorRate: number;
  bottlenecks: string[];
  recommendations: string[];
}

interface NetworkNode {
  id: string;
  type: 'agent' | 'cortex' | 'organ';
  status: 'healthy' | 'warning' | 'critical';
  connections: string[];
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
}

export const HanumanDashboard: React.FC<DashboardProps> = ({ metricsCollector }) => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [networkNodes, setNetworkNodes] = useState<NetworkNode[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState<any[]>([]);
  const [alertsHistory, setAlertsHistory] = useState<any[]>([]);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    // Écoute des événements de métriques
    const handleMetricsUpdate = (data: any) => {
      updateSystemStatus();
      updateNetworkVisualization();
    };

    const handleAlert = (alert: any) => {
      setAlertsHistory(prev => [alert, ...prev.slice(0, 49)]); // Garder 50 alertes max
    };

    metricsCollector.on('aggregated-metrics', handleMetricsUpdate);
    metricsCollector.on('system-analysis', handleMetricsUpdate);
    metricsCollector.on('latency-alert', handleAlert);
    metricsCollector.on('coordination-inefficiency', handleAlert);

    // Mise à jour initiale
    updateSystemStatus();
    updateNetworkVisualization();

    // Mise à jour périodique
    const interval = setInterval(() => {
      updateSystemStatus();
      updateRealTimeMetrics();
    }, 2000);

    return () => {
      metricsCollector.off('aggregated-metrics', handleMetricsUpdate);
      metricsCollector.off('system-analysis', handleMetricsUpdate);
      metricsCollector.off('latency-alert', handleAlert);
      metricsCollector.off('coordination-inefficiency', handleAlert);
      clearInterval(interval);
    };
  }, [metricsCollector]);

  const updateSystemStatus = () => {
    const summary = metricsCollector.getMetricsSummary();
    setSystemStatus({
      healthScore: summary.system.healthScore,
      totalAgents: Object.keys(summary.network).length,
      activeConnections: Object.values(summary.network).flat().length,
      averageLatency: summary.communications.averageLatency,
      coordinationEfficiency: summary.coordination.efficiency,
      errorRate: summary.communications.errorRate,
      bottlenecks: summary.system.bottlenecks,
      recommendations: summary.system.recommendations
    });
  };

  const updateNetworkVisualization = () => {
    const summary = metricsCollector.getMetricsSummary();
    const nodes: NetworkNode[] = [];

    // Création des nœuds à partir de la topologie réseau
    for (const [agentId, connections] of Object.entries(summary.network)) {
      const agentMetrics = metricsCollector.getAgentMetrics(agentId);
      const latestPerformance = agentMetrics.performance[agentMetrics.performance.length - 1];

      nodes.push({
        id: agentId,
        type: agentId.includes('cortex') ? 'cortex' : agentId.includes('organ') ? 'organ' : 'agent',
        status: getNodeStatus(latestPerformance),
        connections: connections as string[],
        metrics: {
          responseTime: latestPerformance?.responseTime.avg || 0,
          throughput: latestPerformance?.throughput || 0,
          errorRate: latestPerformance?.errorRate || 0
        }
      });
    }

    setNetworkNodes(nodes);
    drawNetworkVisualization(nodes);
  };

  const updateRealTimeMetrics = () => {
    const summary = metricsCollector.getMetricsSummary();
    const newMetric = {
      timestamp: Date.now(),
      healthScore: summary.system.healthScore,
      latency: summary.communications.averageLatency,
      efficiency: summary.coordination.efficiency,
      errorRate: summary.communications.errorRate
    };

    setRealTimeMetrics(prev => [...prev.slice(-59), newMetric]); // Garder 60 points max
  };

  const getNodeStatus = (performance: any): 'healthy' | 'warning' | 'critical' => {
    if (!performance) return 'warning';
    
    if (performance.errorRate > 0.1 || performance.responseTime.avg > 1000) {
      return 'critical';
    } else if (performance.errorRate > 0.05 || performance.responseTime.avg > 500) {
      return 'warning';
    }
    return 'healthy';
  };

  const drawNetworkVisualization = (nodes: NetworkNode[]) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Configuration du canvas
    canvas.width = 800;
    canvas.height = 600;
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Positionnement des nœuds en cercle
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 100;

    nodes.forEach((node, index) => {
      const angle = (2 * Math.PI * index) / nodes.length;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      // Dessiner les connexions
      node.connections.forEach(targetId => {
        const targetIndex = nodes.findIndex(n => n.id === targetId);
        if (targetIndex !== -1) {
          const targetAngle = (2 * Math.PI * targetIndex) / nodes.length;
          const targetX = centerX + radius * Math.cos(targetAngle);
          const targetY = centerY + radius * Math.sin(targetAngle);

          ctx.beginPath();
          ctx.moveTo(x, y);
          ctx.lineTo(targetX, targetY);
          ctx.strokeStyle = '#4A90E2';
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      });

      // Dessiner le nœud
      ctx.beginPath();
      ctx.arc(x, y, 20, 0, 2 * Math.PI);
      ctx.fillStyle = getNodeColor(node.status);
      ctx.fill();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Étiquette du nœud
      ctx.fillStyle = '#333';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.id.substring(0, 8), x, y - 30);
    });
  };

  const getNodeColor = (status: string): string => {
    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'critical': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 0.8) return '#4CAF50';
    if (score >= 0.6) return '#FF9800';
    return '#F44336';
  };

  if (!systemStatus) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-xl">Chargement du Dashboard Hanuman...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* En-tête du Dashboard */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            🤖 Dashboard Hanuman - Organisme IA Biomimétique
          </h1>
          
          {/* Métriques principales */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-600">Score de Santé</h3>
              <p className="text-2xl font-bold" style={{ color: getHealthScoreColor(systemStatus.healthScore) }}>
                {(systemStatus.healthScore * 100).toFixed(1)}%
              </p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-green-600">Agents Actifs</h3>
              <p className="text-2xl font-bold text-green-800">{systemStatus.totalAgents}</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-purple-600">Latence Moyenne</h3>
              <p className="text-2xl font-bold text-purple-800">{systemStatus.averageLatency.toFixed(0)}ms</p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-orange-600">Efficacité Coordination</h3>
              <p className="text-2xl font-bold text-orange-800">
                {(systemStatus.coordinationEfficiency * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Visualisation du Réseau Neural */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Réseau Neural Hanuman</h2>
            <canvas
              ref={canvasRef}
              className="w-full border border-gray-200 rounded"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
          </div>

          {/* Alertes et Recommandations */}
          <div className="space-y-6">
            {/* Goulots d'étranglement */}
            {systemStatus.bottlenecks.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-red-800 mb-2">⚠️ Goulots d'étranglement</h3>
                <ul className="space-y-1">
                  {systemStatus.bottlenecks.map((bottleneck, index) => (
                    <li key={index} className="text-red-700 text-sm">• {bottleneck}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Recommandations */}
            {systemStatus.recommendations.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">💡 Recommandations</h3>
                <ul className="space-y-1">
                  {systemStatus.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-blue-700 text-sm">• {recommendation}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Historique des alertes */}
            <div className="bg-white rounded-lg shadow-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">📊 Alertes Récentes</h3>
              <div className="max-h-64 overflow-y-auto">
                {alertsHistory.length === 0 ? (
                  <p className="text-gray-500 text-sm">Aucune alerte récente</p>
                ) : (
                  alertsHistory.slice(0, 10).map((alert, index) => (
                    <div key={index} className="border-b border-gray-200 py-2 last:border-b-0">
                      <p className="text-sm font-medium text-gray-800">{alert.type || 'Alerte'}</p>
                      <p className="text-xs text-gray-600">
                        {new Date(alert.timestamp || Date.now()).toLocaleTimeString()}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
