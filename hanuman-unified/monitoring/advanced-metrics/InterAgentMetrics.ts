/**
 * 🤖 SYSTÈME DE MÉTRIQUES INTER-AGENT AVANCÉ
 *
 * Implémentation des recommandations d'audit pour le monitoring
 * des communications et de l'efficacité de coordination entre agents
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

export interface AgentCommunicationMetric {
  id: string;
  sourceAgent: string;
  targetAgent: string;
  messageType: string;
  timestamp: number;
  latency: number;
  success: boolean;
  payloadSize: number;
  retryCount: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface CoordinationEfficiencyMetric {
  id: string;
  taskId: string;
  involvedAgents: string[];
  coordinationStartTime: number;
  coordinationEndTime: number;
  totalDuration: number;
  consensusTime: number;
  conflictResolutionTime: number;
  successRate: number;
  resourceUtilization: number;
}

export interface AgentPerformanceSnapshot {
  agentId: string;
  timestamp: number;
  responseTime: {
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: number;
  errorRate: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  coordinationScore: number;
  adaptabilityIndex: number;
}

export class InterAgentMetricsCollector extends EventEmitter {
  private communicationMetrics: Map<string, AgentCommunicationMetric[]> = new Map();
  private coordinationMetrics: Map<string, CoordinationEfficiencyMetric[]> = new Map();
  private performanceSnapshots: Map<string, AgentPerformanceSnapshot[]> = new Map();
  private activeConnections: Map<string, Set<string>> = new Map();
  private metricsRetentionPeriod = 24 * 60 * 60 * 1000; // 24 heures

  constructor() {
    super();
    this.startMetricsCollection();
    this.startPeriodicAnalysis();
  }

  /**
   * Enregistre une métrique de communication inter-agent
   */
  recordCommunication(metric: Omit<AgentCommunicationMetric, 'id' | 'timestamp'>): void {
    const communicationMetric: AgentCommunicationMetric = {
      ...metric,
      id: this.generateMetricId(),
      timestamp: Date.now()
    };

    // Stockage par agent source
    if (!this.communicationMetrics.has(metric.sourceAgent)) {
      this.communicationMetrics.set(metric.sourceAgent, []);
    }
    this.communicationMetrics.get(metric.sourceAgent)!.push(communicationMetric);

    // Mise à jour des connexions actives
    this.updateActiveConnections(metric.sourceAgent, metric.targetAgent);

    // Émission d'événement pour monitoring temps réel
    this.emit('communication-recorded', communicationMetric);

    // Analyse de latence en temps réel
    this.analyzeLatencyTrends(communicationMetric);
  }

  /**
   * Enregistre une métrique d'efficacité de coordination
   */
  recordCoordination(metric: Omit<CoordinationEfficiencyMetric, 'id'>): void {
    const coordinationMetric: CoordinationEfficiencyMetric = {
      ...metric,
      id: this.generateMetricId()
    };

    // Stockage par tâche
    if (!this.coordinationMetrics.has(metric.taskId)) {
      this.coordinationMetrics.set(metric.taskId, []);
    }
    this.coordinationMetrics.get(metric.taskId)!.push(coordinationMetric);

    this.emit('coordination-recorded', coordinationMetric);

    // Analyse d'efficacité en temps réel
    this.analyzeCoordinationEfficiency(coordinationMetric);
  }

  /**
   * Capture un snapshot de performance d'agent
   */
  capturePerformanceSnapshot(agentId: string, metrics: Omit<AgentPerformanceSnapshot, 'agentId' | 'timestamp'>): void {
    const snapshot: AgentPerformanceSnapshot = {
      ...metrics,
      agentId,
      timestamp: Date.now()
    };

    if (!this.performanceSnapshots.has(agentId)) {
      this.performanceSnapshots.set(agentId, []);
    }
    this.performanceSnapshots.get(agentId)!.push(snapshot);

    this.emit('performance-snapshot', snapshot);
  }

  /**
   * Analyse les tendances de latence
   */
  private analyzeLatencyTrends(metric: AgentCommunicationMetric): void {
    const recentMetrics = this.getRecentCommunicationMetrics(metric.sourceAgent, 5 * 60 * 1000); // 5 minutes

    if (recentMetrics.length >= 10) {
      const avgLatency = recentMetrics.reduce((sum, m) => sum + m.latency, 0) / recentMetrics.length;
      const latencyTrend = this.calculateTrend(recentMetrics.map(m => m.latency));

      // Alerte si latence augmente significativement
      if (latencyTrend > 0.2 && avgLatency > 100) { // 20% d'augmentation et > 100ms
        this.emit('latency-alert', {
          agentId: metric.sourceAgent,
          avgLatency,
          trend: latencyTrend,
          severity: avgLatency > 500 ? 'critical' : 'warning'
        });
      }
    }
  }

  /**
   * Analyse l'efficacité de coordination
   */
  private analyzeCoordinationEfficiency(metric: CoordinationEfficiencyMetric): void {
    const efficiency = this.calculateCoordinationEfficiency(metric);

    if (efficiency < 0.7) { // Seuil d'efficacité de 70%
      this.emit('coordination-inefficiency', {
        taskId: metric.taskId,
        efficiency,
        involvedAgents: metric.involvedAgents,
        bottlenecks: this.identifyBottlenecks(metric)
      });
    }
  }

  /**
   * Calcule l'efficacité de coordination
   */
  private calculateCoordinationEfficiency(metric: CoordinationEfficiencyMetric): number {
    const idealTime = metric.involvedAgents.length * 50; // 50ms par agent idéalement
    const actualTime = metric.totalDuration;
    const resourceEfficiency = 1 - metric.resourceUtilization;

    return Math.min(1, (idealTime / actualTime) * metric.successRate * resourceEfficiency);
  }

  /**
   * Identifie les goulots d'étranglement
   */
  private identifyBottlenecks(metric: CoordinationEfficiencyMetric): string[] {
    const bottlenecks: string[] = [];

    if (metric.consensusTime > metric.totalDuration * 0.5) {
      bottlenecks.push('consensus-slow');
    }

    if (metric.conflictResolutionTime > metric.totalDuration * 0.3) {
      bottlenecks.push('conflict-resolution-slow');
    }

    if (metric.resourceUtilization > 0.8) {
      bottlenecks.push('resource-contention');
    }

    return bottlenecks;
  }

  /**
   * Obtient les métriques de communication récentes
   */
  private getRecentCommunicationMetrics(agentId: string, timeWindow: number): AgentCommunicationMetric[] {
    const metrics = this.communicationMetrics.get(agentId) || [];
    const cutoff = Date.now() - timeWindow;
    return metrics.filter(m => m.timestamp > cutoff);
  }

  /**
   * Calcule la tendance (pente de régression linéaire simple)
   */
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope / (sumY / n); // Normalisation par la moyenne
  }

  /**
   * Met à jour les connexions actives
   */
  private updateActiveConnections(sourceAgent: string, targetAgent: string): void {
    if (!this.activeConnections.has(sourceAgent)) {
      this.activeConnections.set(sourceAgent, new Set());
    }
    this.activeConnections.get(sourceAgent)!.add(targetAgent);
  }

  /**
   * Génère un ID unique pour les métriques
   */
  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Démarre la collecte de métriques périodique
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.cleanupOldMetrics();
      this.generateAggregatedMetrics();
    }, 60000); // Toutes les minutes
  }

  /**
   * Démarre l'analyse périodique
   */
  private startPeriodicAnalysis(): void {
    setInterval(() => {
      this.performSystemAnalysis();
    }, 5 * 60000); // Toutes les 5 minutes
  }

  /**
   * Nettoie les anciennes métriques
   */
  private cleanupOldMetrics(): void {
    const cutoff = Date.now() - this.metricsRetentionPeriod;

    // Nettoyage des métriques de communication
    for (const [agentId, metrics] of this.communicationMetrics.entries()) {
      const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);
      this.communicationMetrics.set(agentId, filteredMetrics);
    }

    // Nettoyage des snapshots de performance
    for (const [agentId, snapshots] of this.performanceSnapshots.entries()) {
      const filteredSnapshots = snapshots.filter(s => s.timestamp > cutoff);
      this.performanceSnapshots.set(agentId, filteredSnapshots);
    }
  }

  /**
   * Génère des métriques agrégées
   */
  private generateAggregatedMetrics(): void {
    const aggregated = {
      totalCommunications: this.getTotalCommunications(),
      averageLatency: this.getAverageLatency(),
      coordinationEfficiency: this.getOverallCoordinationEfficiency(),
      networkTopology: this.getNetworkTopology(),
      performanceTrends: this.getPerformanceTrends()
    };

    this.emit('aggregated-metrics', aggregated);
  }

  /**
   * Effectue une analyse système complète
   */
  private performSystemAnalysis(): void {
    const analysis = {
      healthScore: this.calculateSystemHealthScore(),
      bottlenecks: this.identifySystemBottlenecks(),
      recommendations: this.generateOptimizationRecommendations(),
      predictions: this.generatePerformancePredictions()
    };

    this.emit('system-analysis', analysis);
  }

  /**
   * Calcule le score de santé du système
   */
  private calculateSystemHealthScore(): number {
    const latencyScore = Math.max(0, 1 - (this.getAverageLatency() / 1000)); // Normalisation sur 1 seconde
    const coordinationScore = this.getOverallCoordinationEfficiency();
    const errorScore = Math.max(0, 1 - this.getAverageErrorRate());

    return (latencyScore + coordinationScore + errorScore) / 3;
  }

  private getTotalCommunications(): number {
    return Array.from(this.communicationMetrics.values())
      .reduce((total, metrics) => total + metrics.length, 0);
  }

  private getAverageLatency(): number {
    const allMetrics = Array.from(this.communicationMetrics.values()).flat();
    if (allMetrics.length === 0) return 0;
    return allMetrics.reduce((sum, m) => sum + m.latency, 0) / allMetrics.length;
  }

  private getOverallCoordinationEfficiency(): number {
    const allMetrics = Array.from(this.coordinationMetrics.values()).flat();
    if (allMetrics.length === 0) return 1;
    return allMetrics.reduce((sum, m) => sum + this.calculateCoordinationEfficiency(m), 0) / allMetrics.length;
  }

  private getAverageErrorRate(): number {
    const allMetrics = Array.from(this.communicationMetrics.values()).flat();
    if (allMetrics.length === 0) return 0;
    const errorCount = allMetrics.filter(m => !m.success).length;
    return errorCount / allMetrics.length;
  }

  private getNetworkTopology(): any {
    const topology = new Map<string, Set<string>>();

    for (const [source, connections] of this.activeConnections.entries()) {
      topology.set(source, new Set(connections));
    }

    return Object.fromEntries(
      Array.from(topology.entries()).map(([key, value]) => [key, Array.from(value)])
    );
  }

  private getPerformanceTrends(): any {
    const trends: any = {};

    for (const [agentId, snapshots] of this.performanceSnapshots.entries()) {
      if (snapshots.length >= 2) {
        const recent = snapshots.slice(-10); // 10 derniers snapshots
        const responseTimes = recent.map(s => s.responseTime.avg);
        const throughputs = recent.map(s => s.throughput);

        trends[agentId] = {
          responseTimeTrend: this.calculateTrend(responseTimes),
          throughputTrend: this.calculateTrend(throughputs),
          currentPerformance: recent[recent.length - 1]
        };
      }
    }

    return trends;
  }

  private identifySystemBottlenecks(): string[] {
    const bottlenecks: string[] = [];
    const avgLatency = this.getAverageLatency();
    const errorRate = this.getAverageErrorRate();
    const coordinationEfficiency = this.getOverallCoordinationEfficiency();

    if (avgLatency > 200) bottlenecks.push('high-latency');
    if (errorRate > 0.05) bottlenecks.push('high-error-rate');
    if (coordinationEfficiency < 0.7) bottlenecks.push('poor-coordination');

    // Analyse des agents les plus lents
    const performanceTrends = this.getPerformanceTrends();
    for (const [agentId, trend] of Object.entries(performanceTrends as any)) {
      if (trend.responseTimeTrend > 0.1) {
        bottlenecks.push(`agent-degradation:${agentId}`);
      }
    }

    return bottlenecks;
  }

  private generateOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const bottlenecks = this.identifySystemBottlenecks();

    if (bottlenecks.includes('high-latency')) {
      recommendations.push('Optimiser les communications inter-agent');
      recommendations.push('Implémenter un cache distribué');
    }

    if (bottlenecks.includes('high-error-rate')) {
      recommendations.push('Améliorer la gestion des erreurs');
      recommendations.push('Implémenter des mécanismes de retry intelligents');
    }

    if (bottlenecks.includes('poor-coordination')) {
      recommendations.push('Optimiser les algorithmes de consensus');
      recommendations.push('Réduire le nombre d\'agents impliqués dans les tâches complexes');
    }

    return recommendations;
  }

  private generatePerformancePredictions(): any {
    const predictions: any = {};
    const trends = this.getPerformanceTrends();

    for (const [agentId, trend] of Object.entries(trends as any)) {
      const currentResponseTime = trend.currentPerformance.responseTime.avg;
      const responseTrend = trend.responseTimeTrend;

      // Prédiction simple basée sur la tendance
      const predictedResponseTime = currentResponseTime * (1 + responseTrend);

      predictions[agentId] = {
        predictedResponseTime,
        riskLevel: predictedResponseTime > 500 ? 'high' : predictedResponseTime > 200 ? 'medium' : 'low',
        recommendedAction: predictedResponseTime > 500 ? 'immediate-optimization' : 'monitor'
      };
    }

    return predictions;
  }

  /**
   * API publique pour obtenir les métriques
   */
  public getMetricsSummary() {
    return {
      communications: {
        total: this.getTotalCommunications(),
        averageLatency: this.getAverageLatency(),
        errorRate: this.getAverageErrorRate()
      },
      coordination: {
        efficiency: this.getOverallCoordinationEfficiency()
      },
      system: {
        healthScore: this.calculateSystemHealthScore(),
        bottlenecks: this.identifySystemBottlenecks(),
        recommendations: this.generateOptimizationRecommendations()
      },
      network: this.getNetworkTopology(),
      trends: this.getPerformanceTrends()
    };
  }

  /**
   * API pour obtenir les métriques d'un agent spécifique
   */
  public getAgentMetrics(agentId: string) {
    const communications = this.communicationMetrics.get(agentId) || [];
    const snapshots = this.performanceSnapshots.get(agentId) || [];

    return {
      agentId,
      communications: communications.slice(-100), // 100 dernières communications
      performance: snapshots.slice(-10), // 10 derniers snapshots
      connections: Array.from(this.activeConnections.get(agentId) || [])
    };
  }
}
}
