/**
 * ⚖️ ÉQUILIBREUR DE CHARGE DYNAMIQUE HANUMAN
 * 
 * Implémentation des recommandations d'audit pour la distribution
 * optimisée de charge entre les agents de l'organisme IA
 */

import { EventEmitter } from 'events';

export interface AgentNode {
  id: string;
  type: string;
  status: 'active' | 'busy' | 'overloaded' | 'offline';
  capacity: number;
  currentLoad: number;
  responseTime: number;
  errorRate: number;
  lastHealthCheck: number;
  capabilities: string[];
  priority: number;
  location?: string;
  metadata: any;
}

export interface LoadBalancingTask {
  id: string;
  type: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number;
  requiredCapabilities: string[];
  payload: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface LoadBalancingStrategy {
  name: string;
  algorithm: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'least_response_time' | 'adaptive' | 'ai_optimized';
  healthCheckInterval: number;
  failoverEnabled: boolean;
  autoScalingEnabled: boolean;
  predictiveEnabled: boolean;
}

export interface LoadBalancingMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  throughput: number;
  agentUtilization: Map<string, number>;
  queueLength: number;
  failoverCount: number;
}

export class DynamicLoadBalancer extends EventEmitter {
  private agents: Map<string, AgentNode> = new Map();
  private taskQueue: LoadBalancingTask[] = [];
  private strategy: LoadBalancingStrategy;
  private metrics: LoadBalancingMetrics;
  private roundRobinIndex = 0;
  private isRunning = false;
  private healthCheckInterval?: NodeJS.Timeout;
  private processingInterval?: NodeJS.Timeout;

  // Algorithmes d'IA pour l'optimisation
  private loadPredictionModel: any = null;
  private performanceHistory: Map<string, number[]> = new Map();
  private taskExecutionHistory: Map<string, number[]> = new Map();

  constructor(strategy: Partial<LoadBalancingStrategy> = {}) {
    super();
    
    this.strategy = {
      name: 'adaptive_ai_optimized',
      algorithm: 'ai_optimized',
      healthCheckInterval: 5000, // 5 secondes
      failoverEnabled: true,
      autoScalingEnabled: true,
      predictiveEnabled: true,
      ...strategy
    };

    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      throughput: 0,
      agentUtilization: new Map(),
      queueLength: 0,
      failoverCount: 0
    };

    this.initializeAIModels();
  }

  /**
   * Démarre l'équilibreur de charge
   */
  start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('⚖️ Équilibreur de charge dynamique Hanuman démarré');

    // Démarrage des vérifications de santé
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, this.strategy.healthCheckInterval);

    // Démarrage du traitement des tâches
    this.processingInterval = setInterval(() => {
      this.processTasks();
    }, 1000); // Traitement toutes les secondes

    this.emit('load-balancer-started');
  }

  /**
   * Arrête l'équilibreur de charge
   */
  stop(): void {
    this.isRunning = false;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    console.log('🛑 Équilibreur de charge dynamique Hanuman arrêté');
    this.emit('load-balancer-stopped');
  }

  /**
   * Enregistre un agent dans l'équilibreur
   */
  registerAgent(agent: Omit<AgentNode, 'lastHealthCheck'>): void {
    const agentNode: AgentNode = {
      ...agent,
      lastHealthCheck: Date.now()
    };

    this.agents.set(agent.id, agentNode);
    this.performanceHistory.set(agent.id, []);
    this.taskExecutionHistory.set(agent.id, []);
    
    console.log(`🤖 Agent ${agent.id} enregistré dans l'équilibreur de charge`);
    this.emit('agent-registered', agentNode);
  }

  /**
   * Désenregistre un agent
   */
  unregisterAgent(agentId: string): boolean {
    const removed = this.agents.delete(agentId);
    this.performanceHistory.delete(agentId);
    this.taskExecutionHistory.delete(agentId);
    
    if (removed) {
      console.log(`🤖 Agent ${agentId} désenregistré de l'équilibreur de charge`);
      this.emit('agent-unregistered', agentId);
    }
    
    return removed;
  }

  /**
   * Soumet une tâche à l'équilibreur
   */
  async submitTask(task: Omit<LoadBalancingTask, 'id' | 'timestamp' | 'retryCount'>): Promise<string> {
    const loadBalancingTask: LoadBalancingTask = {
      ...task,
      id: this.generateTaskId(),
      timestamp: Date.now(),
      retryCount: 0
    };

    this.taskQueue.push(loadBalancingTask);
    this.metrics.totalRequests++;
    this.metrics.queueLength = this.taskQueue.length;

    this.emit('task-submitted', loadBalancingTask);
    
    // Traitement immédiat si possible
    this.processTasks();
    
    return loadBalancingTask.id;
  }

  /**
   * Sélectionne le meilleur agent pour une tâche
   */
  private selectBestAgent(task: LoadBalancingTask): AgentNode | null {
    const availableAgents = Array.from(this.agents.values())
      .filter(agent => this.isAgentSuitable(agent, task));

    if (availableAgents.length === 0) return null;

    switch (this.strategy.algorithm) {
      case 'round_robin':
        return this.selectRoundRobin(availableAgents);
      
      case 'weighted_round_robin':
        return this.selectWeightedRoundRobin(availableAgents);
      
      case 'least_connections':
        return this.selectLeastConnections(availableAgents);
      
      case 'least_response_time':
        return this.selectLeastResponseTime(availableAgents);
      
      case 'adaptive':
        return this.selectAdaptive(availableAgents, task);
      
      case 'ai_optimized':
        return this.selectAIOptimized(availableAgents, task);
      
      default:
        return availableAgents[0];
    }
  }

  /**
   * Vérifie si un agent est adapté pour une tâche
   */
  private isAgentSuitable(agent: AgentNode, task: LoadBalancingTask): boolean {
    // Vérification du statut
    if (agent.status === 'offline' || agent.status === 'overloaded') {
      return false;
    }

    // Vérification des capacités requises
    const hasRequiredCapabilities = task.requiredCapabilities.every(
      capability => agent.capabilities.includes(capability)
    );

    if (!hasRequiredCapabilities) return false;

    // Vérification de la charge
    const loadThreshold = agent.status === 'busy' ? 0.9 : 0.8;
    if (agent.currentLoad / agent.capacity > loadThreshold) {
      return false;
    }

    return true;
  }

  /**
   * Sélection Round Robin
   */
  private selectRoundRobin(agents: AgentNode[]): AgentNode {
    const agent = agents[this.roundRobinIndex % agents.length];
    this.roundRobinIndex++;
    return agent;
  }

  /**
   * Sélection Round Robin Pondéré
   */
  private selectWeightedRoundRobin(agents: AgentNode[]): AgentNode {
    // Calcul des poids basés sur la capacité et la performance
    const weightedAgents = agents.map(agent => ({
      agent,
      weight: agent.capacity * agent.priority * (1 - agent.errorRate)
    }));

    const totalWeight = weightedAgents.reduce((sum, wa) => sum + wa.weight, 0);
    let random = Math.random() * totalWeight;

    for (const wa of weightedAgents) {
      random -= wa.weight;
      if (random <= 0) {
        return wa.agent;
      }
    }

    return weightedAgents[0].agent;
  }

  /**
   * Sélection par Moins de Connexions
   */
  private selectLeastConnections(agents: AgentNode[]): AgentNode {
    return agents.reduce((best, current) => 
      current.currentLoad < best.currentLoad ? current : best
    );
  }

  /**
   * Sélection par Temps de Réponse le Plus Faible
   */
  private selectLeastResponseTime(agents: AgentNode[]): AgentNode {
    return agents.reduce((best, current) => 
      current.responseTime < best.responseTime ? current : best
    );
  }

  /**
   * Sélection Adaptative
   */
  private selectAdaptive(agents: AgentNode[], task: LoadBalancingTask): AgentNode {
    // Calcul d'un score composite pour chaque agent
    const scoredAgents = agents.map(agent => ({
      agent,
      score: this.calculateAdaptiveScore(agent, task)
    }));

    // Tri par score décroissant
    scoredAgents.sort((a, b) => b.score - a.score);
    
    return scoredAgents[0].agent;
  }

  /**
   * Sélection Optimisée par IA
   */
  private selectAIOptimized(agents: AgentNode[], task: LoadBalancingTask): AgentNode {
    if (!this.strategy.predictiveEnabled || !this.loadPredictionModel) {
      return this.selectAdaptive(agents, task);
    }

    // Prédiction de performance pour chaque agent
    const predictions = agents.map(agent => ({
      agent,
      predictedPerformance: this.predictAgentPerformance(agent, task)
    }));

    // Sélection de l'agent avec la meilleure performance prédite
    predictions.sort((a, b) => b.predictedPerformance - a.predictedPerformance);
    
    return predictions[0].agent;
  }

  /**
   * Calcule un score adaptatif pour un agent
   */
  private calculateAdaptiveScore(agent: AgentNode, task: LoadBalancingTask): number {
    const loadScore = 1 - (agent.currentLoad / agent.capacity);
    const responseTimeScore = 1 / (agent.responseTime + 1);
    const errorRateScore = 1 - agent.errorRate;
    const priorityScore = task.priority === 'critical' ? agent.priority / 10 : 1;
    
    // Bonus pour les capacités spécialisées
    const capabilityBonus = task.requiredCapabilities.length > 0 ? 
      task.requiredCapabilities.filter(cap => agent.capabilities.includes(cap)).length / task.requiredCapabilities.length : 1;

    return (loadScore + responseTimeScore + errorRateScore) * priorityScore * capabilityBonus;
  }

  /**
   * Prédit la performance d'un agent pour une tâche
   */
  private predictAgentPerformance(agent: AgentNode, task: LoadBalancingTask): number {
    const history = this.performanceHistory.get(agent.id) || [];
    
    if (history.length < 5) {
      // Pas assez d'historique, utiliser le score adaptatif
      return this.calculateAdaptiveScore(agent, task);
    }

    // Calcul de la tendance de performance
    const recentPerformance = history.slice(-10);
    const trend = this.calculateTrend(recentPerformance);
    const avgPerformance = recentPerformance.reduce((sum, p) => sum + p, 0) / recentPerformance.length;

    // Prédiction basée sur la tendance
    return avgPerformance * (1 + trend);
  }

  /**
   * Traite les tâches en attente
   */
  private async processTasks(): Promise<void> {
    while (this.taskQueue.length > 0 && this.isRunning) {
      const task = this.taskQueue.shift()!;
      const agent = this.selectBestAgent(task);

      if (!agent) {
        // Aucun agent disponible, remettre en queue si pas trop de tentatives
        if (task.retryCount < task.maxRetries) {
          task.retryCount++;
          this.taskQueue.push(task);
        } else {
          this.metrics.failedRequests++;
          this.emit('task-failed', task);
        }
        break;
      }

      // Assignation de la tâche à l'agent
      await this.assignTaskToAgent(task, agent);
    }

    this.metrics.queueLength = this.taskQueue.length;
  }

  /**
   * Assigne une tâche à un agent
   */
  private async assignTaskToAgent(task: LoadBalancingTask, agent: AgentNode): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Mise à jour de la charge de l'agent
      agent.currentLoad++;
      this.updateAgentStatus(agent);

      // Simulation de l'exécution de la tâche
      // Dans une vraie implémentation, ceci ferait un appel à l'agent
      await this.simulateTaskExecution(task, agent);

      const executionTime = Date.now() - startTime;
      
      // Mise à jour des métriques
      this.metrics.successfulRequests++;
      this.updatePerformanceHistory(agent.id, executionTime);
      this.updateTaskExecutionHistory(agent.id, executionTime);

      // Mise à jour de l'agent
      agent.currentLoad--;
      agent.responseTime = (agent.responseTime + executionTime) / 2;
      this.updateAgentStatus(agent);

      this.emit('task-completed', { task, agent, executionTime });

    } catch (error) {
      this.metrics.failedRequests++;
      agent.currentLoad--;
      agent.errorRate = Math.min(1, agent.errorRate + 0.01);
      this.updateAgentStatus(agent);

      // Tentative de failover si activé
      if (this.strategy.failoverEnabled) {
        await this.handleFailover(task, agent);
      }

      this.emit('task-error', { task, agent, error });
    }
  }

  /**
   * Simule l'exécution d'une tâche
   */
  private async simulateTaskExecution(task: LoadBalancingTask, agent: AgentNode): Promise<void> {
    // Simulation d'une exécution avec variabilité basée sur la charge
    const baseTime = task.estimatedDuration;
    const loadFactor = 1 + (agent.currentLoad / agent.capacity);
    const executionTime = baseTime * loadFactor * (0.8 + Math.random() * 0.4);
    
    await new Promise(resolve => setTimeout(resolve, Math.min(executionTime, 5000))); // Max 5 secondes pour la simulation
  }

  /**
   * Met à jour le statut d'un agent
   */
  private updateAgentStatus(agent: AgentNode): void {
    const loadRatio = agent.currentLoad / agent.capacity;
    
    if (loadRatio >= 0.9) {
      agent.status = 'overloaded';
    } else if (loadRatio >= 0.7) {
      agent.status = 'busy';
    } else {
      agent.status = 'active';
    }
  }

  /**
   * Gère le failover en cas d'échec
   */
  private async handleFailover(task: LoadBalancingTask, failedAgent: AgentNode): Promise<void> {
    this.metrics.failoverCount++;
    
    // Marquer l'agent comme problématique temporairement
    failedAgent.status = 'offline';
    
    // Remettre la tâche en queue pour un autre agent
    if (task.retryCount < task.maxRetries) {
      task.retryCount++;
      this.taskQueue.unshift(task); // Priorité haute pour les tâches en failover
    }

    this.emit('failover-triggered', { task, failedAgent });
  }

  /**
   * Effectue les vérifications de santé
   */
  private performHealthChecks(): void {
    const now = Date.now();
    
    for (const agent of this.agents.values()) {
      // Simulation de vérification de santé
      const isHealthy = Math.random() > 0.05; // 95% de chance d'être en bonne santé
      
      if (!isHealthy && agent.status !== 'offline') {
        agent.status = 'offline';
        this.emit('agent-unhealthy', agent);
      } else if (isHealthy && agent.status === 'offline') {
        agent.status = 'active';
        this.emit('agent-recovered', agent);
      }
      
      agent.lastHealthCheck = now;
    }
  }

  // Méthodes utilitaires
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope / (sumY / n);
  }

  private updatePerformanceHistory(agentId: string, performance: number): void {
    const history = this.performanceHistory.get(agentId) || [];
    history.push(performance);
    
    if (history.length > 100) {
      history.shift();
    }
    
    this.performanceHistory.set(agentId, history);
  }

  private updateTaskExecutionHistory(agentId: string, executionTime: number): void {
    const history = this.taskExecutionHistory.get(agentId) || [];
    history.push(executionTime);
    
    if (history.length > 50) {
      history.shift();
    }
    
    this.taskExecutionHistory.set(agentId, history);
  }

  private initializeAIModels(): void {
    // Initialisation des modèles d'IA pour l'optimisation
    // Dans une vraie implémentation, charger des modèles ML pré-entraînés
    this.loadPredictionModel = {
      predict: (features: number[]) => Math.random() * 100 // Simulation
    };
  }

  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // API publique
  public getMetrics(): LoadBalancingMetrics {
    return { ...this.metrics };
  }

  public getAgents(): AgentNode[] {
    return Array.from(this.agents.values());
  }

  public getQueueLength(): number {
    return this.taskQueue.length;
  }

  public getStrategy(): LoadBalancingStrategy {
    return { ...this.strategy };
  }
}
