/**
 * 🧠 CACHE INTELLIGENT HANUMAN
 * 
 * Implémentation des recommandations d'audit pour l'optimisation
 * de la mémoire partagée et la réduction de latence
 */

import { EventEmitter } from 'events';
import { LRUCache } from 'lru-cache';

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  ttl: number;
  size: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  agentId?: string;
  tags: string[];
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  averageResponseTime: number;
  memoryUsage: number;
  evictionCount: number;
  compressionRatio: number;
}

export interface CacheStrategy {
  name: string;
  evictionPolicy: 'lru' | 'lfu' | 'ttl' | 'priority' | 'adaptive';
  compressionEnabled: boolean;
  prefetchEnabled: boolean;
  distributedEnabled: boolean;
  maxSize: number;
  defaultTtl: number;
}

export class IntelligentCache extends EventEmitter {
  private primaryCache: LRUCache<string, CacheEntry>;
  private secondaryCache: Map<string, CacheEntry> = new Map();
  private distributedNodes: Map<string, IntelligentCache> = new Map();
  private metrics: CacheMetrics;
  private strategy: CacheStrategy;
  private compressionEnabled = true;
  private prefetchPatterns: Map<string, string[]> = new Map();
  private accessPatterns: Map<string, number[]> = new Map();

  constructor(strategy: Partial<CacheStrategy> = {}) {
    super();
    
    this.strategy = {
      name: 'adaptive_intelligent',
      evictionPolicy: 'adaptive',
      compressionEnabled: true,
      prefetchEnabled: true,
      distributedEnabled: true,
      maxSize: 10000,
      defaultTtl: 5 * 60 * 1000, // 5 minutes
      ...strategy
    };

    this.primaryCache = new LRUCache({
      max: this.strategy.maxSize,
      ttl: this.strategy.defaultTtl,
      updateAgeOnGet: true,
      allowStale: false
    });

    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      evictionCount: 0,
      compressionRatio: 0
    };

    this.startMetricsCollection();
    this.startPrefetchAnalysis();
  }

  /**
   * Récupère une valeur du cache avec intelligence adaptative
   */
  async get<T>(key: string, agentId?: string): Promise<T | null> {
    const startTime = performance.now();
    this.metrics.totalRequests++;

    try {
      // Recherche dans le cache primaire
      let entry = this.primaryCache.get(key) as CacheEntry<T>;
      
      if (entry) {
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.recordAccess(key, agentId);
        this.metrics.totalHits++;
        
        // Décompression si nécessaire
        const value = await this.decompress(entry.value);
        
        this.emit('cache-hit', { key, agentId, responseTime: performance.now() - startTime });
        return value;
      }

      // Recherche dans le cache secondaire
      entry = this.secondaryCache.get(key) as CacheEntry<T>;
      if (entry && !this.isExpired(entry)) {
        // Promotion vers le cache primaire
        this.primaryCache.set(key, entry);
        this.secondaryCache.delete(key);
        
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.recordAccess(key, agentId);
        this.metrics.totalHits++;
        
        const value = await this.decompress(entry.value);
        this.emit('cache-hit-secondary', { key, agentId, responseTime: performance.now() - startTime });
        return value;
      }

      // Recherche dans les nœuds distribués
      if (this.strategy.distributedEnabled) {
        for (const [nodeId, node] of this.distributedNodes.entries()) {
          const distributedValue = await node.get<T>(key, agentId);
          if (distributedValue !== null) {
            // Mise en cache locale
            await this.set(key, distributedValue, { agentId, fromDistributed: true });
            this.emit('cache-hit-distributed', { key, agentId, nodeId, responseTime: performance.now() - startTime });
            return distributedValue;
          }
        }
      }

      // Cache miss
      this.metrics.totalMisses++;
      this.emit('cache-miss', { key, agentId, responseTime: performance.now() - startTime });
      
      // Déclenchement du prefetch si pattern détecté
      this.triggerPrefetch(key, agentId);
      
      return null;

    } finally {
      this.updateMetrics(performance.now() - startTime);
    }
  }

  /**
   * Stocke une valeur dans le cache avec optimisations intelligentes
   */
  async set<T>(
    key: string, 
    value: T, 
    options: {
      ttl?: number;
      priority?: 'low' | 'medium' | 'high' | 'critical';
      agentId?: string;
      tags?: string[];
      fromDistributed?: boolean;
    } = {}
  ): Promise<void> {
    const {
      ttl = this.strategy.defaultTtl,
      priority = 'medium',
      agentId,
      tags = [],
      fromDistributed = false
    } = options;

    // Compression intelligente
    const compressedValue = await this.compress(value);
    const size = this.calculateSize(compressedValue);

    const entry: CacheEntry<T> = {
      key,
      value: compressedValue,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      ttl,
      size,
      priority,
      agentId,
      tags
    };

    // Stratégie d'éviction intelligente
    if (this.shouldEvict(entry)) {
      await this.performIntelligentEviction(entry);
    }

    // Stockage dans le cache primaire
    this.primaryCache.set(key, entry);

    // Distribution aux nœuds si activée et pas déjà distribué
    if (this.strategy.distributedEnabled && !fromDistributed) {
      this.distributeToNodes(key, entry);
    }

    // Analyse des patterns pour le prefetch
    this.analyzeAccessPattern(key, agentId);

    this.emit('cache-set', { key, agentId, size, priority });
  }

  /**
   * Supprime une entrée du cache
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.primaryCache.delete(key);
    this.secondaryCache.delete(key);

    // Suppression des nœuds distribués
    if (this.strategy.distributedEnabled) {
      for (const node of this.distributedNodes.values()) {
        await node.delete(key);
      }
    }

    this.emit('cache-delete', { key });
    return deleted;
  }

  /**
   * Vide le cache avec options
   */
  async clear(options: { tags?: string[]; agentId?: string } = {}): Promise<void> {
    const { tags, agentId } = options;

    if (tags || agentId) {
      // Suppression sélective
      for (const [key, entry] of this.primaryCache.entries()) {
        if (this.matchesFilter(entry, { tags, agentId })) {
          this.primaryCache.delete(key);
        }
      }
      
      for (const [key, entry] of this.secondaryCache.entries()) {
        if (this.matchesFilter(entry, { tags, agentId })) {
          this.secondaryCache.delete(key);
        }
      }
    } else {
      // Suppression complète
      this.primaryCache.clear();
      this.secondaryCache.clear();
    }

    this.emit('cache-clear', options);
  }

  /**
   * Compression intelligente des données
   */
  private async compress<T>(value: T): Promise<T | string> {
    if (!this.strategy.compressionEnabled) return value;

    try {
      const serialized = JSON.stringify(value);
      
      // Compression seulement si la valeur est assez grande
      if (serialized.length > 1000) {
        // Simulation de compression (dans une vraie implémentation, utiliser zlib ou similaire)
        const compressed = `compressed:${serialized.length}:${serialized.substring(0, 100)}...`;
        this.metrics.compressionRatio = serialized.length / compressed.length;
        return compressed as T;
      }
      
      return value;
    } catch (error) {
      console.warn('Erreur de compression:', error);
      return value;
    }
  }

  /**
   * Décompression intelligente des données
   */
  private async decompress<T>(value: T): Promise<T> {
    if (!this.strategy.compressionEnabled) return value;

    try {
      if (typeof value === 'string' && value.startsWith('compressed:')) {
        // Simulation de décompression
        const parts = value.split(':');
        const originalLength = parseInt(parts[1]);
        // Dans une vraie implémentation, décompresser ici
        return value; // Retourner la valeur décompressée
      }
      
      return value;
    } catch (error) {
      console.warn('Erreur de décompression:', error);
      return value;
    }
  }

  /**
   * Éviction intelligente basée sur plusieurs facteurs
   */
  private async performIntelligentEviction(newEntry: CacheEntry): Promise<void> {
    const candidates = Array.from(this.primaryCache.entries())
      .map(([key, entry]) => ({ key, entry: entry as CacheEntry }))
      .filter(({ entry }) => entry.priority !== 'critical');

    if (candidates.length === 0) return;

    // Calcul du score d'éviction pour chaque candidat
    const scoredCandidates = candidates.map(({ key, entry }) => ({
      key,
      entry,
      score: this.calculateEvictionScore(entry)
    }));

    // Tri par score (plus bas = plus susceptible d'être évincé)
    scoredCandidates.sort((a, b) => a.score - b.score);

    // Éviction du candidat avec le plus bas score
    const toEvict = scoredCandidates[0];
    
    // Déplacement vers le cache secondaire si encore valide
    if (!this.isExpired(toEvict.entry)) {
      this.secondaryCache.set(toEvict.key, toEvict.entry);
    }
    
    this.primaryCache.delete(toEvict.key);
    this.metrics.evictionCount++;
    
    this.emit('cache-eviction', { key: toEvict.key, score: toEvict.score });
  }

  /**
   * Calcule le score d'éviction pour une entrée
   */
  private calculateEvictionScore(entry: CacheEntry): number {
    const now = Date.now();
    const age = now - entry.timestamp;
    const timeSinceLastAccess = now - entry.lastAccessed;
    const priorityWeight = { low: 1, medium: 2, high: 3, critical: 4 }[entry.priority];
    
    // Score basé sur l'âge, fréquence d'accès, priorité et taille
    const ageScore = age / (24 * 60 * 60 * 1000); // Normalisation sur 24h
    const accessScore = 1 / (entry.accessCount + 1);
    const recencyScore = timeSinceLastAccess / (60 * 60 * 1000); // Normalisation sur 1h
    const sizeScore = entry.size / 10000; // Normalisation sur 10KB
    
    return (ageScore + accessScore + recencyScore + sizeScore) / priorityWeight;
  }

  /**
   * Analyse des patterns d'accès pour le prefetch
   */
  private analyzeAccessPattern(key: string, agentId?: string): void {
    if (!agentId) return;

    const pattern = this.accessPatterns.get(agentId) || [];
    pattern.push(Date.now());
    
    // Garder seulement les 100 derniers accès
    if (pattern.length > 100) {
      pattern.shift();
    }
    
    this.accessPatterns.set(agentId, pattern);

    // Détection de patterns de prefetch
    this.detectPrefetchPatterns(key, agentId);
  }

  /**
   * Détecte les patterns pour le prefetch
   */
  private detectPrefetchPatterns(key: string, agentId: string): void {
    const relatedKeys = this.prefetchPatterns.get(key) || [];
    
    // Logique simplifiée de détection de patterns
    // Dans une vraie implémentation, utiliser des algorithmes plus sophistiqués
    
    this.prefetchPatterns.set(key, relatedKeys);
  }

  /**
   * Déclenche le prefetch basé sur les patterns
   */
  private triggerPrefetch(key: string, agentId?: string): void {
    if (!this.strategy.prefetchEnabled) return;

    const relatedKeys = this.prefetchPatterns.get(key);
    if (relatedKeys && relatedKeys.length > 0) {
      this.emit('prefetch-triggered', { key, relatedKeys, agentId });
    }
  }

  // Méthodes utilitaires
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private shouldEvict(newEntry: CacheEntry): boolean {
    return this.primaryCache.size >= this.strategy.maxSize;
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length;
  }

  private matchesFilter(entry: CacheEntry, filter: { tags?: string[]; agentId?: string }): boolean {
    if (filter.agentId && entry.agentId !== filter.agentId) return false;
    if (filter.tags && !filter.tags.some(tag => entry.tags.includes(tag))) return false;
    return true;
  }

  private recordAccess(key: string, agentId?: string): void {
    // Enregistrement des accès pour l'analyse
  }

  private distributeToNodes(key: string, entry: CacheEntry): void {
    // Distribution aux nœuds du cache distribué
    for (const node of this.distributedNodes.values()) {
      node.set(key, entry.value, { 
        ttl: entry.ttl, 
        priority: entry.priority, 
        agentId: entry.agentId,
        tags: entry.tags,
        fromDistributed: true 
      });
    }
  }

  private updateMetrics(responseTime: number): void {
    this.metrics.hitRate = this.metrics.totalHits / this.metrics.totalRequests;
    this.metrics.missRate = this.metrics.totalMisses / this.metrics.totalRequests;
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + responseTime) / 2;
    this.metrics.memoryUsage = this.primaryCache.size + this.secondaryCache.size;
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      this.emit('metrics-update', this.metrics);
    }, 30000); // Toutes les 30 secondes
  }

  private startPrefetchAnalysis(): void {
    setInterval(() => {
      this.analyzePrefetchOpportunities();
    }, 60000); // Toutes les minutes
  }

  private analyzePrefetchOpportunities(): void {
    // Analyse des opportunités de prefetch
    this.emit('prefetch-analysis', {
      patterns: this.prefetchPatterns.size,
      opportunities: Array.from(this.prefetchPatterns.keys()).length
    });
  }

  // API publique
  public getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  public getStrategy(): CacheStrategy {
    return { ...this.strategy };
  }

  public addDistributedNode(nodeId: string, node: IntelligentCache): void {
    this.distributedNodes.set(nodeId, node);
  }

  public removeDistributedNode(nodeId: string): boolean {
    return this.distributedNodes.delete(nodeId);
  }
}
