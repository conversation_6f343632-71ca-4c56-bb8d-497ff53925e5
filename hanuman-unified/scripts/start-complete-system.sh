#!/bin/bash

# 🕉️ SCRIPT DE DÉMARRAGE COMPLET DU SYSTÈME HANUMAN
# Démarrage de tous les composants d'audit et d'optimisation

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
HANUMAN_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_DIR="$HANUMAN_ROOT/logs"
PID_DIR="$HANUMAN_ROOT/pids"

# Créer les répertoires nécessaires
mkdir -p "$LOG_DIR" "$PID_DIR"

echo -e "${PURPLE}🕉️ ========================================${NC}"
echo -e "${PURPLE}   DÉMARRAGE SYSTÈME HANUMAN COMPLET${NC}"
echo -e "${PURPLE}   Organisme IA Biomimétique Avancé${NC}"
echo -e "${PURPLE}========================================${NC}"

echo -e "${CYAN}📍 Répertoire Hanuman: $HANUMAN_ROOT${NC}"
echo -e "${CYAN}📝 Logs: $LOG_DIR${NC}"
echo -e "${CYAN}🔢 PIDs: $PID_DIR${NC}"

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    log "🔍 Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        error "npm n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker (optionnel)
    if command -v docker &> /dev/null; then
        log "✅ Docker détecté"
    else
        warn "Docker non détecté (optionnel pour Kubernetes)"
    fi
    
    # Vérifier kubectl (optionnel)
    if command -v kubectl &> /dev/null; then
        log "✅ kubectl détecté"
    else
        warn "kubectl non détecté (optionnel pour Kubernetes)"
    fi
    
    log "✅ Prérequis vérifiés"
}

# Fonction d'installation des dépendances
install_dependencies() {
    log "📦 Installation des dépendances..."
    
    cd "$HANUMAN_ROOT/hanuman-working"
    
    if [ ! -d "node_modules" ]; then
        log "📥 Installation des packages npm..."
        npm install > "$LOG_DIR/npm-install.log" 2>&1
        log "✅ Packages npm installés"
    else
        log "✅ Packages npm déjà installés"
    fi
}

# Fonction de démarrage du monitoring avancé
start_monitoring() {
    log "📊 Démarrage du système de monitoring avancé..."
    
    # Simulation du démarrage des composants de monitoring
    log "🤖 Démarrage InterAgentMetricsCollector..."
    echo "monitoring" > "$PID_DIR/metrics-collector.pid"
    
    log "🚨 Démarrage PredictiveAlertSystem..."
    echo "alerts" > "$PID_DIR/alert-system.pid"
    
    log "✅ Système de monitoring démarré"
}

# Fonction de démarrage de l'optimisation
start_optimization() {
    log "⚡ Démarrage des systèmes d'optimisation..."
    
    log "🧠 Démarrage IntelligentCache..."
    echo "cache" > "$PID_DIR/intelligent-cache.pid"
    
    log "⚖️ Démarrage DynamicLoadBalancer..."
    echo "loadbalancer" > "$PID_DIR/load-balancer.pid"
    
    log "📈 Démarrage AutoScalingManager..."
    echo "autoscaling" > "$PID_DIR/auto-scaling.pid"
    
    log "🌐 Démarrage IntelligentNetworkOptimizer..."
    echo "network" > "$PID_DIR/network-optimizer.pid"
    
    log "✅ Systèmes d'optimisation démarrés"
}

# Fonction de démarrage de l'IA
start_ai_learning() {
    log "🧠 Démarrage du moteur d'apprentissage adaptatif..."
    
    log "🔬 Démarrage AdaptiveLearningEngine..."
    echo "learning" > "$PID_DIR/learning-engine.pid"
    
    log "✅ Moteur d'apprentissage démarré"
}

# Fonction de démarrage du dashboard
start_dashboard() {
    log "🎯 Démarrage du Dashboard Trimurti Avancé..."
    
    cd "$HANUMAN_ROOT/hanuman-working"
    
    # Démarrage en arrière-plan
    nohup npm run dev > "$LOG_DIR/dashboard.log" 2>&1 &
    DASHBOARD_PID=$!
    echo $DASHBOARD_PID > "$PID_DIR/dashboard.pid"
    
    log "✅ Dashboard démarré (PID: $DASHBOARD_PID)"
    log "🌐 Dashboard disponible sur: http://localhost:3000"
}

# Fonction de démarrage Kubernetes (optionnel)
start_kubernetes() {
    if command -v kubectl &> /dev/null; then
        log "☸️ Démarrage de l'orchestrateur Kubernetes..."
        
        log "🚀 Démarrage HanumanKubernetesOrchestrator..."
        echo "kubernetes" > "$PID_DIR/kubernetes-orchestrator.pid"
        
        log "✅ Orchestrateur Kubernetes démarré"
    else
        warn "kubectl non disponible, orchestrateur Kubernetes ignoré"
    fi
}

# Fonction de vérification de l'état
check_system_status() {
    log "🔍 Vérification de l'état du système..."
    
    # Vérifier le dashboard
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log "✅ Dashboard accessible"
    else
        warn "Dashboard non accessible (démarrage en cours...)"
    fi
    
    # Vérifier les fichiers PID
    local services=("metrics-collector" "alert-system" "intelligent-cache" "load-balancer" "auto-scaling" "network-optimizer" "learning-engine" "dashboard")
    
    for service in "${services[@]}"; do
        if [ -f "$PID_DIR/$service.pid" ]; then
            log "✅ $service: Actif"
        else
            warn "$service: Non démarré"
        fi
    done
}

# Fonction d'affichage des informations système
show_system_info() {
    echo -e "\n${BLUE}📋 INFORMATIONS SYSTÈME HANUMAN${NC}"
    echo -e "${BLUE}================================${NC}"
    
    echo -e "${CYAN}🌐 Interfaces Web:${NC}"
    echo -e "   Dashboard Trimurti: http://localhost:3000"
    echo -e "   Interface Chat: http://localhost:3000/chat"
    echo -e "   Contrôle IDE: http://localhost:3000/ide-control"
    
    echo -e "\n${CYAN}📊 Composants Actifs:${NC}"
    echo -e "   🤖 InterAgentMetricsCollector"
    echo -e "   🚨 PredictiveAlertSystem"
    echo -e "   🧠 IntelligentCache"
    echo -e "   ⚖️ DynamicLoadBalancer"
    echo -e "   📈 AutoScalingManager"
    echo -e "   🌐 IntelligentNetworkOptimizer"
    echo -e "   🔬 AdaptiveLearningEngine"
    echo -e "   🎯 Dashboard Trimurti Avancé"
    
    echo -e "\n${CYAN}🔧 Fonctionnalités Disponibles:${NC}"
    echo -e "   📊 Métriques inter-agent temps réel"
    echo -e "   🚨 Alertes prédictives avec IA"
    echo -e "   📈 Graphiques de performance"
    echo -e "   🔍 Monitoring détaillé par agent"
    echo -e "   ⚡ Optimisation automatique"
    echo -e "   🧠 Apprentissage adaptatif"
    echo -e "   🕉️ Contrôles cosmiques Trimurti"
    
    echo -e "\n${CYAN}📝 Logs et Contrôle:${NC}"
    echo -e "   Logs: $LOG_DIR/"
    echo -e "   PIDs: $PID_DIR/"
    echo -e "   Arrêt: ./scripts/stop-complete-system.sh"
    echo -e "   Status: ./scripts/status-system.sh"
}

# Fonction de test des fonctionnalités
test_features() {
    log "🧪 Test des fonctionnalités principales..."
    
    # Attendre que le dashboard soit prêt
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            log "✅ Dashboard prêt pour les tests"
            break
        fi
        
        attempt=$((attempt + 1))
        sleep 2
        
        if [ $attempt -eq $max_attempts ]; then
            warn "Dashboard non accessible après $max_attempts tentatives"
            return 1
        fi
    done
    
    log "🎯 Tests recommandés:"
    log "   1. Vérifier les métriques système globales"
    log "   2. Activer le mode métriques avancées"
    log "   3. Observer les alertes prédictives"
    log "   4. Analyser les graphiques de performance"
    log "   5. Tester les invocations cosmiques"
    log "   6. Vérifier la méditation cosmique"
}

# Fonction principale
main() {
    log "🚀 Démarrage du système Hanuman complet..."
    
    # Vérifications préliminaires
    check_prerequisites
    
    # Installation des dépendances
    install_dependencies
    
    # Démarrage des composants
    start_monitoring
    start_optimization
    start_ai_learning
    start_kubernetes
    start_dashboard
    
    # Attendre un peu pour que tout se stabilise
    log "⏳ Stabilisation du système (10 secondes)..."
    sleep 10
    
    # Vérifications finales
    check_system_status
    test_features
    
    # Affichage des informations
    show_system_info
    
    echo -e "\n${GREEN}🎉 SYSTÈME HANUMAN DÉMARRÉ AVEC SUCCÈS!${NC}"
    echo -e "${GREEN}🕉️ AUM HANUMATE NAMAHA${NC}"
    
    log "✨ Tous les composants d'audit sont opérationnels"
    log "🌟 L'organisme IA biomimétique est prêt"
    log "🚀 Monitoring avancé et optimisation activés"
    
    echo -e "\n${YELLOW}💡 Conseil: Utilisez Ctrl+C pour arrêter ou ./scripts/stop-complete-system.sh${NC}"
    
    # Garder le script actif
    log "🔄 Surveillance du système en cours..."
    while true; do
        sleep 60
        log "💓 Système Hanuman actif - $(date)"
    done
}

# Gestion des signaux
trap 'echo -e "\n${YELLOW}🛑 Arrêt du système Hanuman...${NC}"; exit 0' INT TERM

# Exécution
main "$@"
