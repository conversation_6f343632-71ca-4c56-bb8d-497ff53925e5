#!/bin/bash

# 🛑 SCRIPT D'ARRÊT COMPLET DU SYSTÈME HANUMAN
# Arrêt propre de tous les composants

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
HANUMAN_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PID_DIR="$HANUMAN_ROOT/pids"
LOG_DIR="$HANUMAN_ROOT/logs"

echo -e "${PURPLE}🛑 ========================================${NC}"
echo -e "${PURPLE}   ARRÊT SYSTÈME HANUMAN COMPLET${NC}"
echo -e "${PURPLE}   Organisme IA Biomimétique${NC}"
echo -e "${PURPLE}========================================${NC}"

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Fonction d'arrêt d'un service par PID
stop_service() {
    local service_name=$1
    local pid_file="$PID_DIR/$service_name.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log "🛑 Arrêt de $service_name (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 2
            
            # Vérifier si le processus est toujours actif
            if kill -0 "$pid" 2>/dev/null; then
                warn "Arrêt forcé de $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            log "✅ $service_name arrêté"
        else
            warn "$service_name n'était pas actif"
        fi
        
        rm -f "$pid_file"
    else
        warn "Fichier PID non trouvé pour $service_name"
    fi
}

# Fonction d'arrêt du dashboard
stop_dashboard() {
    log "🎯 Arrêt du Dashboard Trimurti..."
    
    # Arrêter par PID si disponible
    if [ -f "$PID_DIR/dashboard.pid" ]; then
        local pid=$(cat "$PID_DIR/dashboard.pid")
        if kill -0 "$pid" 2>/dev/null; then
            log "🛑 Arrêt du dashboard (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 3
            
            if kill -0 "$pid" 2>/dev/null; then
                warn "Arrêt forcé du dashboard..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$PID_DIR/dashboard.pid"
    fi
    
    # Arrêter tous les processus Node.js sur le port 3000
    local node_pids=$(lsof -ti:3000 2>/dev/null || true)
    if [ -n "$node_pids" ]; then
        log "🛑 Arrêt des processus Node.js sur le port 3000..."
        echo "$node_pids" | xargs kill 2>/dev/null || true
        sleep 2
        
        # Vérification et arrêt forcé si nécessaire
        node_pids=$(lsof -ti:3000 2>/dev/null || true)
        if [ -n "$node_pids" ]; then
            warn "Arrêt forcé des processus Node.js..."
            echo "$node_pids" | xargs kill -9 2>/dev/null || true
        fi
    fi
    
    log "✅ Dashboard arrêté"
}

# Fonction d'arrêt des composants de monitoring
stop_monitoring() {
    log "📊 Arrêt du système de monitoring..."
    
    stop_service "metrics-collector"
    stop_service "alert-system"
    
    log "✅ Système de monitoring arrêté"
}

# Fonction d'arrêt des composants d'optimisation
stop_optimization() {
    log "⚡ Arrêt des systèmes d'optimisation..."
    
    stop_service "intelligent-cache"
    stop_service "load-balancer"
    stop_service "auto-scaling"
    stop_service "network-optimizer"
    
    log "✅ Systèmes d'optimisation arrêtés"
}

# Fonction d'arrêt de l'IA
stop_ai_learning() {
    log "🧠 Arrêt du moteur d'apprentissage..."
    
    stop_service "learning-engine"
    
    log "✅ Moteur d'apprentissage arrêté"
}

# Fonction d'arrêt Kubernetes
stop_kubernetes() {
    log "☸️ Arrêt de l'orchestrateur Kubernetes..."
    
    stop_service "kubernetes-orchestrator"
    
    log "✅ Orchestrateur Kubernetes arrêté"
}

# Fonction de nettoyage
cleanup() {
    log "🧹 Nettoyage des fichiers temporaires..."
    
    # Nettoyer les fichiers PID orphelins
    if [ -d "$PID_DIR" ]; then
        find "$PID_DIR" -name "*.pid" -type f -delete 2>/dev/null || true
        log "✅ Fichiers PID nettoyés"
    fi
    
    # Nettoyer les logs anciens (optionnel)
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.log" -mtime +7 -delete 2>/dev/null || true
        log "✅ Logs anciens nettoyés"
    fi
    
    # Nettoyer les processus Node.js orphelins
    local orphan_pids=$(ps aux | grep -E "(hanuman|trimurti)" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$orphan_pids" ]; then
        warn "Nettoyage des processus orphelins..."
        echo "$orphan_pids" | xargs kill 2>/dev/null || true
    fi
}

# Fonction de vérification de l'arrêt
verify_shutdown() {
    log "🔍 Vérification de l'arrêt complet..."
    
    # Vérifier le port 3000
    if lsof -ti:3000 >/dev/null 2>&1; then
        warn "Le port 3000 est encore utilisé"
        return 1
    fi
    
    # Vérifier les processus Hanuman
    local hanuman_processes=$(ps aux | grep -E "(hanuman|trimurti)" | grep -v grep | wc -l)
    if [ "$hanuman_processes" -gt 0 ]; then
        warn "$hanuman_processes processus Hanuman encore actifs"
        return 1
    fi
    
    log "✅ Arrêt complet vérifié"
    return 0
}

# Fonction d'affichage du statut final
show_shutdown_status() {
    echo -e "\n${BLUE}📋 STATUT D'ARRÊT HANUMAN${NC}"
    echo -e "${BLUE}===========================${NC}"
    
    echo -e "${CYAN}🛑 Composants Arrêtés:${NC}"
    echo -e "   🤖 InterAgentMetricsCollector"
    echo -e "   🚨 PredictiveAlertSystem"
    echo -e "   🧠 IntelligentCache"
    echo -e "   ⚖️ DynamicLoadBalancer"
    echo -e "   📈 AutoScalingManager"
    echo -e "   🌐 IntelligentNetworkOptimizer"
    echo -e "   🔬 AdaptiveLearningEngine"
    echo -e "   🎯 Dashboard Trimurti"
    echo -e "   ☸️ Orchestrateur Kubernetes"
    
    echo -e "\n${CYAN}🧹 Nettoyage Effectué:${NC}"
    echo -e "   📁 Fichiers PID supprimés"
    echo -e "   📝 Logs anciens nettoyés"
    echo -e "   🔄 Processus orphelins terminés"
    
    echo -e "\n${CYAN}🔄 Redémarrage:${NC}"
    echo -e "   ./scripts/start-complete-system.sh"
}

# Fonction principale
main() {
    log "🛑 Arrêt du système Hanuman complet..."
    
    # Arrêt des composants dans l'ordre inverse du démarrage
    stop_dashboard
    stop_ai_learning
    stop_optimization
    stop_monitoring
    stop_kubernetes
    
    # Nettoyage
    cleanup
    
    # Vérification
    if verify_shutdown; then
        show_shutdown_status
        
        echo -e "\n${GREEN}✅ SYSTÈME HANUMAN ARRÊTÉ AVEC SUCCÈS!${NC}"
        echo -e "${GREEN}🕉️ AUM HANUMATE NAMAHA${NC}"
        
        log "🌟 Tous les composants ont été arrêtés proprement"
        log "🧹 Nettoyage terminé"
        log "💤 L'organisme IA biomimétique est en repos"
        
    else
        error "Arrêt incomplet détecté"
        warn "Certains processus peuvent encore être actifs"
        warn "Vérifiez manuellement avec: ps aux | grep hanuman"
        exit 1
    fi
}

# Gestion des arguments
case "${1:-}" in
    --force)
        log "🔥 Arrêt forcé demandé"
        # Arrêt forcé de tous les processus
        pkill -f "hanuman" 2>/dev/null || true
        pkill -f "trimurti" 2>/dev/null || true
        pkill -f "node.*3000" 2>/dev/null || true
        cleanup
        log "✅ Arrêt forcé terminé"
        ;;
    --help|-h)
        echo "Usage: $0 [--force] [--help]"
        echo ""
        echo "Options:"
        echo "  --force    Arrêt forcé de tous les processus"
        echo "  --help     Affiche cette aide"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        error "Option inconnue: $1"
        echo "Utilisez --help pour l'aide"
        exit 1
        ;;
esac
