{"name": "agentic-coding-framework-rb2", "version": "3.8.1", "description": "Framework de développement agentic de classe mondiale pour Retreat And Be avec <PERSON>uman - l'organisme IA vivant", "main": "index.js", "type": "module", "scripts": {"start": "npm run start:hanuman && npm run start:frontend", "start:hanuman": "cd hanuman-unified && ./scripts/start-hanuman.sh", "start:frontend": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run dev", "start:backend": "cd Projet-RB2/Backend-NestJS && npm run start:dev", "start:all": "concurrently \"npm run start:hanuman\" \"npm run start:frontend\" \"npm run start:backend\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run build", "build:backend": "cd Projet-RB2/Backend-NestJS && npm run build", "build:storybook": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run build-storybook", "test": "npm run test:unit && npm run test:e2e", "test:unit": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run test", "test:e2e": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run test:e2e", "test:e2e:ui": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run test:e2e:ui", "test:cypress": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run test:cypress", "test:cypress:open": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run test:cypress:open", "storybook": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run storybook", "storybook:build": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run build-storybook", "monitoring": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run dev", "performance": "cd Projet-RB2/Front-Audrey-V1-Main-main && node scripts/measure-performance.js", "roadmap:update": "cd hanuman-unified && ./scripts/update-roadmap-sprint13.sh", "roadmap:status": "cd hanuman-unified/sandbox/roadmap && cat roadmap_system_status.json", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "k8s:deploy": "./scripts/deploy-production.sh", "k8s:status": "kubectl get pods -n retreat-and-be", "install:all": "npm run install:root && npm run install:hanuman && npm run install:frontend && npm run install:backend", "install:root": "npm install", "install:hanuman": "cd hanuman-unified && npm install", "install:frontend": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm install", "install:backend": "cd Projet-RB2/Backend-NestJS && npm install", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd Projet-RB2/Front-Audrey-V1-Main-main && npm run lint", "lint:backend": "cd Projet-RB2/Backend-NestJS && npm run lint", "lint:fix": "npm run lint:frontend -- --fix && npm run lint:backend -- --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "security:audit": "npm audit && cd Projet-RB2/Front-Audrey-V1-Main-main && npm audit && cd ../Backend-NestJS && npm audit", "security:fix": "npm audit fix && cd Projet-RB2/Front-Audrey-V1-Main-main && npm audit fix && cd ../Backend-NestJS && npm audit fix", "clean": "npm run clean:node_modules && npm run clean:dist", "clean:node_modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "clean:dist": "find . -name 'dist' -type d -prune -exec rm -rf '{}' +", "docs:generate": "npm run storybook:build && npm run docs:api", "docs:api": "cd Projet-RB2/Backend-NestJS && npm run docs", "docs:serve": "npm run storybook", "health:check": "./scripts/health-check.sh", "monitor:performance": "./scripts/monitor-performance.sh", "backup": "./scripts/backup-restore.sh backup", "restore": "./scripts/backup-restore.sh restore"}, "keywords": ["agentic", "framework", "ai", "microservices", "retreat-and-be", "hanuman", "react", "<PERSON><PERSON><PERSON>", "typescript", "kubernetes", "design-system", "monitoring", "e2e-testing"], "author": {"name": "Retreat And Be Team", "email": "<EMAIL>", "url": "https://retreatandbe.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/retreatandbe/agentic-framework-rb2.git"}, "bugs": {"url": "https://github.com/retreatandbe/agentic-framework-rb2/issues"}, "homepage": "https://github.com/retreatandbe/agentic-framework-rb2#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "workbox-cli": "^7.3.0", "workbox-webpack-plugin": "^7.3.0"}, "workspaces": ["hanuman-unified", "Projet-RB2/Front-Audrey-V1-Main-main", "Projet-RB2/Backend-NestJS", "Projet-RB2/Agent IA", "Projet-RB2/Financial-Management", "Projet-RB2/Social", "Projet-RB2/Security", "vimana"], "config": {"framework": {"version": "3.8.1", "name": "Agentic-Coding-Framework-RB2", "hanuman": {"enabled": true, "port": 3000}, "frontend": {"port": 5173, "storybook": 6006}, "backend": {"port": 3001}, "monitoring": {"enabled": true, "realtime": true}, "testing": {"e2e": true, "playwright": true, "cypress": true}}}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "volta": {"node": "18.18.0", "npm": "9.8.1"}, "dependencies": {"@loadable/component": "^5.16.7", "@types/serviceworker": "^0.0.136", "react-intersection-observer": "^9.16.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-window": "^7.3.0"}}